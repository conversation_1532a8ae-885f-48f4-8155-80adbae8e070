using System;
using System.IO;
using System.Threading.Tasks;
using System.Linq;

namespace AWSAutoRegister.Services
{
    public class EmailVerificationService
    {
        private static readonly string RequestFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmailCodeRequest.txt");
        private static readonly string ResponseFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmailCodeResponse.txt");
        private const int TimeoutMinutes = 2; // 2分钟超时
        private const int InitialDelaySeconds = 2; // 初始延迟2秒
        private const int RetryIntervalSeconds = 3; // 3秒重试间隔

        public async Task<(bool Success, string? VerificationCode, string Message)> GetVerificationCodeAsync(string email)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[邮箱验证] 开始邮箱验证码获取流程，邮箱: {email}");
                Console.WriteLine($"[邮箱验证] 开始邮箱验证码获取流程，邮箱: {email}");
                System.Diagnostics.Debug.WriteLine($"[邮箱验证] 请求文件路径: {RequestFile}");
                System.Diagnostics.Debug.WriteLine($"[邮箱验证] 响应文件路径: {ResponseFile}");
                Console.WriteLine($"[邮箱验证] 请求文件路径: {RequestFile}");
                Console.WriteLine($"[邮箱验证] 响应文件路径: {ResponseFile}");

                // 确保删除旧的请求和响应文件
                try
                {
                    if (File.Exists(RequestFile))
                    {
                        File.Delete(RequestFile);
                        System.Diagnostics.Debug.WriteLine($"[邮箱验证] 已删除旧的请求文件");
                    }
                    if (File.Exists(ResponseFile))
                    {
                        File.Delete(ResponseFile);
                        System.Diagnostics.Debug.WriteLine($"[邮箱验证] 已删除旧的响应文件");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[邮箱验证] 删除旧文件失败: {ex.Message}");
                }

                // 第一次触发前等待2秒
                System.Diagnostics.Debug.WriteLine($"[邮箱验证] 等待{InitialDelaySeconds}秒后开始第一次触发...");
                Console.WriteLine($"[邮箱验证] 等待{InitialDelaySeconds}秒后开始第一次触发...");
                await Task.Delay(InitialDelaySeconds * 1000);

                // 等待响应文件，2分钟超时
                var endTime = DateTime.Now.AddMinutes(TimeoutMinutes);
                var retryCount = 0;
                var lastTriggerTime = DateTime.Now.AddSeconds(-10); // 设置为足够早的时间，确保首次立即触发
                var triggerInterval = TimeSpan.FromSeconds(2); // 每2秒触发一次获取验证码按钮
                bool hasReceivedCode = false;

                string? currentTimestamp = null;

                while (DateTime.Now < endTime && !hasReceivedCode)
                {
                    // 检查是否需要触发获取验证码按钮
                    if (DateTime.Now - lastTriggerTime >= triggerInterval)
                    {
                        try
                        {
                            retryCount++;
                            // 创建新的触发请求
                            currentTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
                            var triggerContent = $"getCode|{email}|{currentTimestamp}";

                            // 安全写入文件，避免冲突
                            await WriteRequestFileAsync(RequestFile, triggerContent);
                            lastTriggerTime = DateTime.Now;

                            System.Diagnostics.Debug.WriteLine($"[邮箱验证] 第{retryCount}次触发获取验证码按钮");
                            Console.WriteLine($"[邮箱验证] 第{retryCount}次触发获取验证码按钮");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"[邮箱验证] 触发失败: {ex.Message}");
                        }
                    }

                    // 等待3秒后检查响应
                    await Task.Delay(RetryIntervalSeconds * 1000);

                    // 立即检查是否已经获取到验证码，如果是则跳出循环
                    if (hasReceivedCode)
                    {
                        System.Diagnostics.Debug.WriteLine($"[邮箱验证] 检测到验证码已获取，停止重复请求");
                        Console.WriteLine($"[邮箱验证] 检测到验证码已获取，停止重复请求");
                        break;
                    }

                    // 检查是否有响应文件
                    if (File.Exists(ResponseFile))
                    {
                        System.Diagnostics.Debug.WriteLine($"[邮箱验证] 找到响应文件，正在读取...");
                        try
                        {
                            var responseContent = await ReadResponseFileAsync(ResponseFile);
                            System.Diagnostics.Debug.WriteLine($"[邮箱验证] 响应内容: {responseContent}");

                            if (!string.IsNullOrEmpty(responseContent))
                            {
                                var parts = responseContent.Split('|');
                                if (parts.Length >= 3)
                                {
                                    var status = parts[0];
                                    var data = parts[1];
                                    var responseTimestamp = parts[2];

                                    // 放宽时间戳匹配条件，或者跳过时间戳检查
                                    bool timestampMatches = true;
                                    if (currentTimestamp != null && long.TryParse(responseTimestamp, out var respTime) && long.TryParse(currentTimestamp, out var reqTime))
                                    {
                                        timestampMatches = Math.Abs(respTime - reqTime) < 600000; // 10分钟误差
                                    }

                                    if (timestampMatches)
                                    {
                                        // 安全删除响应文件
                                        await SafeDeleteFileAsync(ResponseFile);

                                        if (status == "success")
                                        {
                                            // 检查验证码是否有效
                                            if (IsValidVerificationCode(data))
                                            {
                                                System.Diagnostics.Debug.WriteLine($"[邮箱验证] 获取到有效验证码: {data}，立即停止重复请求");
                                                Console.WriteLine($"[邮箱验证] 获取到有效验证码: {data}，立即停止重复请求");
                                                hasReceivedCode = true;

                                                // 立即清理请求文件，防止其他线程继续处理
                                                try
                                                {
                                                    if (File.Exists(RequestFile))
                                                    {
                                                        File.Delete(RequestFile);
                                                        System.Diagnostics.Debug.WriteLine($"[邮箱验证] 已清理请求文件，停止重复触发");
                                                    }
                                                }
                                                catch (Exception cleanupEx)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"[邮箱验证] 清理请求文件失败: {cleanupEx.Message}");
                                                }

                                                return (true, data, $"验证码获取成功（第{retryCount}次尝试）");
                                            }
                                            else
                                            {
                                                // 无效验证码（如010001），继续重试
                                                System.Diagnostics.Debug.WriteLine($"[邮箱验证] 获取到无效验证码: {data}，继续重试");
                                            }
                                        }
                                        else if (status == "login_failed")
                                        {
                                            // 邮箱登录失败，立即返回失败
                                            System.Diagnostics.Debug.WriteLine($"[邮箱验证] 邮箱登录失败: {data}");
                                            Console.WriteLine($"[邮箱验证] 邮箱登录失败: {data}");
                                            return (false, null, $"邮箱登录失败: {data}");
                                        }
                                        else
                                        {
                                            // 获取失败，继续重试
                                            System.Diagnostics.Debug.WriteLine($"[邮箱验证] 获取验证码失败: {data}，继续重试");
                                        }
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"[邮箱验证] 时间戳不匹配，跳过此响应");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // 文件可能正在被写入，继续等待
                            System.Diagnostics.Debug.WriteLine($"[邮箱验证] 读取响应文件失败: {ex.Message}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"[邮箱验证] 获取验证码超时（{TimeoutMinutes}分钟）");
                Console.WriteLine($"[邮箱验证] 获取验证码超时（{TimeoutMinutes}分钟）");
                return (false, null, $"获取验证码超时（{TimeoutMinutes}分钟内未获取到有效验证码）");
            }
            catch (Exception ex)
            {
                return (false, null, $"获取验证码失败: {ex.Message}");
            }
            finally
            {
                // 清理请求文件
                try
                {
                    if (File.Exists(RequestFile))
                    {
                        File.Delete(RequestFile);
                    }
                }
                catch { }
            }
        }

        private static async Task WriteRequestFileAsync(string filePath, string content)
        {
            // 安全写入文件，避免冲突
            var maxRetries = 3;
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    await File.WriteAllTextAsync(filePath, content);
                    return;
                }
                catch (IOException) when (i < maxRetries - 1)
                {
                    // 文件被占用，等待50ms后重试
                    await Task.Delay(50);
                }
            }
        }

        private static async Task<string> ReadResponseFileAsync(string filePath)
        {
            // 安全读取文件，避免冲突
            var maxRetries = 3;
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    return await File.ReadAllTextAsync(filePath);
                }
                catch (IOException) when (i < maxRetries - 1)
                {
                    // 文件被占用，等待50ms后重试
                    await Task.Delay(50);
                }
            }
            return string.Empty;
        }

        private static Task SafeDeleteFileAsync(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[邮箱验证] 删除文件失败: {ex.Message}");
                // 不抛出异常，继续执行
            }
            return Task.CompletedTask;
        }

        private static bool IsValidVerificationCode(string? code)
        {
            // 检查是否是有效的验证码
            if (string.IsNullOrEmpty(code))
                return false;

            // 检查是否是无效的默认值（如010001）
            if (code == "010001" || code == "000000" || code == "123456")
                return false;

            // 检查是否是6位数字
            if (code.Length != 6 || !code.All(char.IsDigit))
                return false;

            return true;
        }
    }
}
