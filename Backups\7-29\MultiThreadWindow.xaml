<Window x:Class="AWSAutoRegister.MultiThreadWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:AWSAutoRegister"
        Title="多线程注册管理" Width="650" Height="608"
        MinWidth="550" MinHeight="608"
        WindowStartupLocation="Manual"
        ResizeMode="CanResize"
        Icon="favicon.ico"
        UseLayoutRounding="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        SnapsToDevicePixels="True"
        Background="#F5F7FA">

    <Window.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4299E1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#000000"
                                                  Opacity="0.1"
                                                  ShadowDepth="2"
                                                  BlurRadius="8"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#3182CE"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#2C5282"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="Background" Value="#E2E8F0"/>
                                <Setter Property="Foreground" Value="#A0AEC0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 线程状态卡片样式 -->
        <Style x:Key="ThreadCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000"
                                      Opacity="0.08"
                                      ShadowDepth="4"
                                      BlurRadius="16"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 状态指示器样式 -->
        <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
        </Style>

        <!-- 进度条样式 -->
        <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Height" Value="8"/>
            <Setter Property="Background" Value="#E2E8F0"/>
            <Setter Property="Foreground" Value="#4299E1"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E2E8F0" BorderThickness="0,0,0,1" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧标题 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🚀 多线程注册管理"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="#2D3748"/>
                </StackPanel>

                <!-- 右侧控制按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">

                    <Button Name="PauseAllBtn"
                            Content="⏸ 暂停所有"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="#ED8936"
                            Margin="0,0,8,0"
                            Click="PauseAllBtn_Click"/>
                    <Button Name="ResumeAllBtn"
                            Content="▶ 所有继续"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="#48BB78"
                            Margin="0,0,8,0"
                            Click="ResumeAllBtn_Click"/>
                    <Button Name="TerminateAllBtn"
                            Content="⏹ 终止所有"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="#E53E3E"
                            Margin="0,0,8,0"
                            Click="TerminateAllBtn_Click"/>
                    <Button Name="BackToMainBtn"
                            Content="🏠 返回主窗口"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="#805AD5"
                            Click="BackToMainBtn_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 线程状态列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <ItemsControl Name="ThreadListView" Margin="16">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="2"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource ThreadCardStyle}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 线程标题和状态 -->
                                <Grid Grid.Row="0" Margin="0,0,0,12">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                                        <TextBlock Text="{Binding ThreadTitle}"
                                                   FontSize="14"
                                                   FontWeight="Bold"
                                                   Foreground="#2D3748"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                                        <Ellipse Style="{StaticResource StatusIndicatorStyle}"
                                                 Fill="#4299E1"/>
                                        <TextBlock Text="{Binding Status.StatusText}"
                                                   FontSize="12"
                                                   FontWeight="Medium"
                                                   Foreground="{Binding Status.Status, Converter={x:Static local:StatusToColorConverter.Instance}}"/>
                                    </StackPanel>
                                </Grid>

                                <!-- 当前操作 -->
                                <TextBlock Grid.Row="1"
                                           Text="{Binding Status.CurrentOperation}"
                                           FontSize="14"
                                           Foreground="#2D3748"
                                           Margin="0,0,0,8"/>

                                <!-- 状态消息 -->
                                <TextBlock Grid.Row="2"
                                           Text="{Binding Status.Message}"
                                           FontSize="12"
                                           Foreground="{Binding Status.Status, Converter={x:Static local:StatusToColorConverter.Instance}}"
                                           TextWrapping="Wrap"
                                           Margin="0,0,0,12"/>

                                <!-- 进度条 -->
                                <StackPanel Grid.Row="3" Margin="0,0,0,12">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ProgressBar Grid.Column="0"
                                                     Value="{Binding Status.Progress}"
                                                     Maximum="100"
                                                     Style="{StaticResource ModernProgressBarStyle}"/>
                                        <TextBlock Grid.Column="1"
                                                   Text="{Binding Status.Progress, StringFormat={}{0}%}"
                                                   FontSize="11"
                                                   Foreground="#718096"
                                                   Margin="8,0,0,0"/>
                                    </Grid>
                                </StackPanel>

                                <!-- 控制按钮 -->
                                <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right">
                                    <Button x:Name="PauseThreadBtn"
                                            Content="⏸ 暂停注册"
                                            Style="{StaticResource ModernButtonStyle}"
                                            Background="#ED8936"
                                            FontSize="11"
                                            Padding="6,3"
                                            Margin="0,0,3,0"
                                            Click="PauseThreadBtn_Click"
                                            Tag="{Binding ThreadId}"
                                            IsEnabled="{Binding IsPauseEnabled}"/>
                                    <Button x:Name="ContinueThreadBtn"
                                            Content="▶ 继续注册"
                                            Style="{StaticResource ModernButtonStyle}"
                                            Background="#48BB78"
                                            FontSize="11"
                                            Padding="6,3"
                                            Margin="0,0,3,0"
                                            Click="ContinueThreadBtn_Click"
                                            Tag="{Binding ThreadId}"
                                            IsEnabled="{Binding IsContinueEnabled}"/>
                                    <Button x:Name="TerminateThreadBtn"
                                            Content="⏹ 终止注册"
                                            Style="{StaticResource ModernButtonStyle}"
                                            Background="#E53E3E"
                                            FontSize="11"
                                            Padding="6,3"
                                            Click="TerminateThreadBtn_Click"
                                            Tag="{Binding ThreadId}"
                                            IsEnabled="{Binding IsTerminateEnabled}"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E2E8F0" BorderThickness="0,1,0,0" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Name="StatusText"
                           Text="多线程管理器已就绪"
                           FontSize="12"
                           Foreground="#4A5568"/>

                <!-- 总进度显示 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,16,0">
                    <TextBlock Text="总进度:"
                               FontSize="12"
                               FontWeight="Medium"
                               Foreground="#4A5568"
                               Margin="0,0,8,0"/>
                    <ProgressBar Name="OverallProgress"
                                 Width="120"
                                 Height="6"
                                 Style="{StaticResource ModernProgressBarStyle}"
                                 Margin="0,0,8,0"/>
                    <TextBlock Name="OverallProgressText"
                               Text="0%"
                               FontSize="11"
                               FontWeight="Medium"
                               Foreground="#4A5568"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="活跃线程:"
                               FontSize="12"
                               Foreground="#4A5568"
                               Margin="0,0,4,0"/>
                    <TextBlock Name="ActiveThreadsText"
                               Text="0"
                               FontSize="12"
                               FontWeight="Bold"
                               Foreground="#2D3748"
                               Margin="0,0,16,0"/>

                    <TextBlock Text="总线程:"
                               FontSize="12"
                               Foreground="#4A5568"
                               Margin="0,0,4,0"/>
                    <TextBlock Name="TotalThreadsText"
                               Text="0"
                               FontSize="12"
                               FontWeight="Bold"
                               Foreground="#2D3748"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
