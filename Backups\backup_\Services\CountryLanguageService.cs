using System;
using System.Collections.Generic;
using System.Linq;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 国家代码到语言代码映射服务
    /// </summary>
    public static class CountryLanguageService
    {
        // 国家代码到语言代码的映射表（ISO 639-1语言代码）
        private static readonly Dictionary<string, string> CountryToLanguageMapping = new()
        {
            // 英语国家
            ["US"] = "en-US",    // 美国
            ["GB"] = "en-GB",    // 英国
            ["CA"] = "en-CA",    // 加拿大
            ["AU"] = "en-AU",    // 澳大利亚
            ["NZ"] = "en-NZ",    // 新西兰
            ["IE"] = "en-IE",    // 爱尔兰
            ["ZA"] = "en-ZA",    // 南非

            // 西班牙语国家
            ["ES"] = "es-ES",    // 西班牙
            ["MX"] = "es-MX",    // 墨西哥
            ["AR"] = "es-AR",    // 阿根廷
            ["CL"] = "es-CL",    // 智利
            ["CO"] = "es-CO",    // 哥伦比亚
            ["PE"] = "es-PE",    // 秘鲁
            ["VE"] = "es-VE",    // 委内瑞拉
            ["EC"] = "es-EC",    // 厄瓜多尔
            ["BO"] = "es-BO",    // 玻利维亚
            ["PY"] = "es-PY",    // 巴拉圭
            ["UY"] = "es-UY",    // 乌拉圭
            ["CR"] = "es-CR",    // 哥斯达黎加
            ["PA"] = "es-PA",    // 巴拿马
            ["GT"] = "es-GT",    // 危地马拉
            ["HN"] = "es-HN",    // 洪都拉斯
            ["SV"] = "es-SV",    // 萨尔瓦多
            ["NI"] = "es-NI",    // 尼加拉瓜
            ["DO"] = "es-DO",    // 多米尼加
            ["CU"] = "es-CU",    // 古巴

            // 法语国家
            ["FR"] = "fr-FR",    // 法国
            ["BE"] = "fr-BE",    // 比利时（法语区）
            ["CH"] = "fr-CH",    // 瑞士（法语区）
            ["LU"] = "fr-LU",    // 卢森堡
            ["MC"] = "fr-MC",    // 摩纳哥

            // 德语国家
            ["DE"] = "de-DE",    // 德国
            ["AT"] = "de-AT",    // 奥地利
            ["LI"] = "de-LI",    // 列支敦士登

            // 意大利语国家
            ["IT"] = "it-IT",    // 意大利
            ["SM"] = "it-SM",    // 圣马力诺
            ["VA"] = "it-VA",    // 梵蒂冈

            // 葡萄牙语国家
            ["PT"] = "pt-PT",    // 葡萄牙
            ["BR"] = "pt-BR",    // 巴西

            // 荷兰语国家
            ["NL"] = "nl-NL",    // 荷兰

            // 北欧语言
            ["SE"] = "sv-SE",    // 瑞典
            ["NO"] = "nb-NO",    // 挪威
            ["DK"] = "da-DK",    // 丹麦
            ["FI"] = "fi-FI",    // 芬兰
            ["IS"] = "is-IS",    // 冰岛

            // 东欧语言
            ["PL"] = "pl-PL",    // 波兰
            ["CZ"] = "cs-CZ",    // 捷克
            ["SK"] = "sk-SK",    // 斯洛伐克
            ["HU"] = "hu-HU",    // 匈牙利
            ["RO"] = "ro-RO",    // 罗马尼亚
            ["BG"] = "bg-BG",    // 保加利亚
            ["HR"] = "hr-HR",    // 克罗地亚
            ["SI"] = "sl-SI",    // 斯洛文尼亚
            ["EE"] = "et-EE",    // 爱沙尼亚
            ["LV"] = "lv-LV",    // 拉脱维亚
            ["LT"] = "lt-LT",    // 立陶宛

            // 俄语国家
            ["RU"] = "ru-RU",    // 俄罗斯
            ["BY"] = "ru-BY",    // 白俄罗斯
            ["KZ"] = "ru-KZ",    // 哈萨克斯坦

            // 亚洲语言
            ["CN"] = "zh-CN",    // 中国（简体中文）
            ["TW"] = "zh-TW",    // 台湾（繁体中文）
            ["HK"] = "zh-HK",    // 香港（繁体中文）
            ["JP"] = "ja-JP",    // 日本
            ["KR"] = "ko-KR",    // 韩国
            ["TH"] = "th-TH",    // 泰国
            ["VN"] = "vi-VN",    // 越南
            ["ID"] = "id-ID",    // 印度尼西亚
            ["MY"] = "ms-MY",    // 马来西亚
            ["SG"] = "en-SG",    // 新加坡（英语）
            ["PH"] = "en-PH",    // 菲律宾（英语）
            ["IN"] = "hi-IN",    // 印度（印地语）

            // 阿拉伯语国家
            ["SA"] = "ar-SA",    // 沙特阿拉伯
            ["AE"] = "ar-AE",    // 阿联酋
            ["EG"] = "ar-EG",    // 埃及
            ["JO"] = "ar-JO",    // 约旦
            ["LB"] = "ar-LB",    // 黎巴嫩
            ["SY"] = "ar-SY",    // 叙利亚
            ["IQ"] = "ar-IQ",    // 伊拉克
            ["KW"] = "ar-KW",    // 科威特
            ["QA"] = "ar-QA",    // 卡塔尔
            ["BH"] = "ar-BH",    // 巴林
            ["OM"] = "ar-OM",    // 阿曼
            ["YE"] = "ar-YE",    // 也门
            ["MA"] = "ar-MA",    // 摩洛哥
            ["TN"] = "ar-TN",    // 突尼斯
            ["DZ"] = "ar-DZ",    // 阿尔及利亚
            ["LY"] = "ar-LY",    // 利比亚

            // 土耳其语
            ["TR"] = "tr-TR",    // 土耳其

            // 希腊语
            ["GR"] = "el-GR",    // 希腊

            // 希伯来语
            ["IL"] = "he-IL",    // 以色列

            // 波斯语
            ["IR"] = "fa-IR",    // 伊朗

            // 其他语言
            ["UA"] = "uk-UA",    // 乌克兰
            ["RS"] = "sr-RS",    // 塞尔维亚
            ["BA"] = "bs-BA",    // 波斯尼亚
            ["MK"] = "mk-MK",    // 北马其顿
            ["AL"] = "sq-AL",    // 阿尔巴尼亚
            ["MT"] = "mt-MT",    // 马耳他
            ["CY"] = "el-CY",    // 塞浦路斯（希腊语）
        };

        // 国家代码到时区的映射表
        private static readonly Dictionary<string, string[]> CountryToTimezoneMapping = new()
        {
            // 北美洲
            ["US"] = new[] { "America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles" },
            ["CA"] = new[] { "America/Toronto", "America/Vancouver", "America/Montreal", "Canada/Pacific" },
            ["MX"] = new[] { "America/Mexico_City", "America/Tijuana", "America/Cancun" },

            // 欧洲
            ["GB"] = new[] { "Europe/London" },
            ["FR"] = new[] { "Europe/Paris" },
            ["DE"] = new[] { "Europe/Berlin" },
            ["IT"] = new[] { "Europe/Rome" },
            ["ES"] = new[] { "Europe/Madrid" },
            ["PT"] = new[] { "Europe/Lisbon" },
            ["NL"] = new[] { "Europe/Amsterdam" },
            ["BE"] = new[] { "Europe/Brussels" },
            ["CH"] = new[] { "Europe/Zurich" },
            ["AT"] = new[] { "Europe/Vienna" },
            ["SE"] = new[] { "Europe/Stockholm" },
            ["NO"] = new[] { "Europe/Oslo" },
            ["DK"] = new[] { "Europe/Copenhagen" },
            ["FI"] = new[] { "Europe/Helsinki" },
            ["PL"] = new[] { "Europe/Warsaw" },
            ["CZ"] = new[] { "Europe/Prague" },
            ["HU"] = new[] { "Europe/Budapest" },
            ["RO"] = new[] { "Europe/Bucharest" },
            ["BG"] = new[] { "Europe/Sofia" },
            ["GR"] = new[] { "Europe/Athens" },
            ["RU"] = new[] { "Europe/Moscow", "Asia/Yekaterinburg", "Asia/Novosibirsk" },
            ["UA"] = new[] { "Europe/Kiev" },
            ["TR"] = new[] { "Europe/Istanbul" },

            // 亚洲
            ["CN"] = new[] { "Asia/Shanghai" },
            ["JP"] = new[] { "Asia/Tokyo" },
            ["KR"] = new[] { "Asia/Seoul" },
            ["TH"] = new[] { "Asia/Bangkok" },
            ["VN"] = new[] { "Asia/Ho_Chi_Minh" },
            ["ID"] = new[] { "Asia/Jakarta", "Asia/Makassar" },
            ["MY"] = new[] { "Asia/Kuala_Lumpur" },
            ["SG"] = new[] { "Asia/Singapore" },
            ["PH"] = new[] { "Asia/Manila" },
            ["IN"] = new[] { "Asia/Kolkata" },
            ["HK"] = new[] { "Asia/Hong_Kong" },
            ["TW"] = new[] { "Asia/Taipei" },

            // 中东
            ["SA"] = new[] { "Asia/Riyadh" },
            ["AE"] = new[] { "Asia/Dubai" },
            ["IL"] = new[] { "Asia/Jerusalem" },
            ["IR"] = new[] { "Asia/Tehran" },
            ["EG"] = new[] { "Africa/Cairo" },
            ["JO"] = new[] { "Asia/Amman" },
            ["LB"] = new[] { "Asia/Beirut" },
            ["KW"] = new[] { "Asia/Kuwait" },
            ["QA"] = new[] { "Asia/Qatar" },
            ["BH"] = new[] { "Asia/Bahrain" },
            ["OM"] = new[] { "Asia/Muscat" },

            // 大洋洲
            ["AU"] = new[] { "Australia/Sydney", "Australia/Melbourne", "Australia/Perth" },
            ["NZ"] = new[] { "Pacific/Auckland" },

            // 南美洲
            ["BR"] = new[] { "America/Sao_Paulo", "America/Manaus", "America/Fortaleza" },
            ["AR"] = new[] { "America/Argentina/Buenos_Aires" },
            ["CL"] = new[] { "America/Santiago" },
            ["CO"] = new[] { "America/Bogota" },
            ["PE"] = new[] { "America/Lima" },
            ["VE"] = new[] { "America/Caracas" },
            ["EC"] = new[] { "America/Guayaquil" },
            ["UY"] = new[] { "America/Montevideo" },

            // 非洲
            ["ZA"] = new[] { "Africa/Johannesburg" },
            ["MA"] = new[] { "Africa/Casablanca" },
            ["TN"] = new[] { "Africa/Tunis" },
            ["DZ"] = new[] { "Africa/Algiers" },
            ["LY"] = new[] { "Africa/Tripoli" }
        };

        // 国家代码到地理位置的映射表（每个国家的多个主要城市坐标）
        private static readonly Dictionary<string, (double Latitude, double Longitude)[]> CountryToGeolocationMapping = new()
        {
            // 北美洲
            ["US"] = new[]
            {
                (40.7128, -74.0060),   // 纽约
                (34.0522, -118.2437),  // 洛杉矶
                (41.8781, -87.6298),   // 芝加哥
                (29.7604, -95.3698),   // 休斯顿
                (33.4484, -112.0740),  // 凤凰城
                (39.9526, -75.1652),   // 费城
                (29.4241, -98.4936),   // 圣安东尼奥
                (32.7767, -96.7970),   // 达拉斯
                (37.3382, -121.8863),  // 圣何塞
                (30.2672, -97.7431)    // 奥斯汀
            },
            ["CA"] = new[]
            {
                (43.6532, -79.3832),   // 多伦多
                (49.2827, -123.1207),  // 温哥华
                (45.5017, -73.5673),   // 蒙特利尔
                (51.0447, -114.0719),  // 卡尔加里
                (53.5461, -113.4938),  // 埃德蒙顿
                (45.4215, -75.6972)    // 渥太华
            },
            ["MX"] = new[]
            {
                (19.4326, -99.1332),   // 墨西哥城
                (25.6866, -100.3161),  // 蒙特雷
                (20.6597, -103.3496),  // 瓜达拉哈拉
                (32.5149, -117.0382)   // 蒂华纳
            },

            // 欧洲
            ["GB"] = new[]
            {
                (51.5074, -0.1278),    // 伦敦
                (53.4808, -2.2426),    // 曼彻斯特
                (52.4862, -1.8904),    // 伯明翰
                (55.9533, -3.1883),    // 爱丁堡
                (53.8008, -1.5491),    // 利兹
                (51.4545, -2.5879)     // 布里斯托
            },
            ["FR"] = new[]
            {
                (48.8566, 2.3522),     // 巴黎
                (43.2965, 5.3698),     // 马赛
                (45.7640, 4.8357),     // 里昂
                (43.6047, 1.4442),     // 图卢兹
                (47.2184, -1.5536),    // 南特
                (43.7102, 7.2620)      // 尼斯
            },
            ["DE"] = new[]
            {
                (52.5200, 13.4050),    // 柏林
                (53.5511, 9.9937),     // 汉堡
                (48.1351, 11.5820),    // 慕尼黑
                (50.9375, 6.9603),     // 科隆
                (50.1109, 8.6821),     // 法兰克福
                (51.2277, 6.7735)      // 杜塞尔多夫
            },
            ["IT"] = new[]
            {
                (41.9028, 12.4964),    // 罗马
                (45.4642, 9.1900),     // 米兰
                (40.8518, 14.2681),    // 那不勒斯
                (45.4408, 12.3155),    // 威尼斯
                (43.7696, 11.2558),    // 佛罗伦萨
                (38.1157, 13.3615)     // 巴勒莫
            },
            ["ES"] = new[]
            {
                (40.4168, -3.7038),    // 马德里
                (41.3851, 2.1734),     // 巴塞罗那
                (37.3891, -5.9845),    // 塞维利亚
                (39.4699, -0.3763),    // 瓦伦西亚
                (43.2627, -2.9253),    // 毕尔巴鄂
                (36.7213, -4.4214)     // 马拉加
            },

            // 亚洲
            ["CN"] = new[]
            {
                (39.9042, 116.4074),   // 北京
                (31.2304, 121.4737),   // 上海
                (23.1291, 113.2644),   // 广州
                (22.3193, 114.1694),   // 香港
                (30.5728, 104.0668),   // 成都
                (39.3434, 117.3616),   // 天津
                (29.5630, 106.5516),   // 重庆
                (36.0611, 120.3758)    // 青岛
            },
            ["JP"] = new[]
            {
                (35.6762, 139.6503),   // 东京
                (34.6937, 135.5023),   // 大阪
                (35.0116, 135.7681),   // 京都
                (35.1815, 136.9066),   // 名古屋
                (43.0642, 141.3469),   // 札幌
                (34.3853, 132.4553)    // 广岛
            },
            ["KR"] = new[]
            {
                (37.5665, 126.9780),   // 首尔
                (35.1796, 129.0756),   // 釜山
                (37.4563, 126.7052),   // 仁川
                (35.8714, 128.6014),   // 大邱
                (36.3504, 127.3845)    // 大田
            },

            // 大洋洲
            ["AU"] = new[]
            {
                (-33.8688, 151.2093),  // 悉尼
                (-37.8136, 144.9631),  // 墨尔本
                (-27.4698, 153.0251),  // 布里斯班
                (-31.9505, 115.8605),  // 珀斯
                (-34.9285, 138.6007)   // 阿德莱德
            },
            ["NZ"] = new[]
            {
                (-36.8485, 174.7633),  // 奥克兰
                (-41.2865, 174.7762),  // 惠灵顿
                (-43.5321, 172.6362)   // 基督城
            },

            // 南美洲
            ["BR"] = new[]
            {
                (-23.5505, -46.6333),  // 圣保罗
                (-22.9068, -43.1729),  // 里约热内卢
                (-15.8267, -47.9218),  // 巴西利亚
                (-12.9714, -38.5014),  // 萨尔瓦多
                (-25.4284, -49.2733)   // 库里蒂巴
            },
            ["AR"] = new[]
            {
                (-34.6118, -58.3960),  // 布宜诺斯艾利斯
                (-31.4201, -64.1888),  // 科尔多瓦
                (-24.7821, -65.4232)   // 萨尔塔
            },
            ["CL"] = new[]
            {
                (-33.4489, -70.6693),  // 圣地亚哥
                (-36.8201, -73.0444),  // 康塞普西翁
                (-41.4693, -72.9424)   // 瓦尔迪维亚
            },

            // 亚洲其他国家
            ["IN"] = new[]
            {
                (28.7041, 77.1025),    // 新德里
                (19.0760, 72.8777),    // 孟买
                (12.9716, 77.5946),    // 班加罗尔
                (22.5726, 88.3639),    // 加尔各答
                (13.0827, 80.2707)     // 钦奈
            },
            ["TH"] = new[]
            {
                (13.7563, 100.5018),   // 曼谷
                (18.7883, 98.9853),    // 清迈
                (12.9236, 100.8824)    // 芭堤雅
            },
            ["VN"] = new[]
            {
                (21.0285, 105.8542),   // 河内
                (10.8231, 106.6297),   // 胡志明市
                (16.0544, 108.2022)    // 岘港
            },
            ["ID"] = new[]
            {
                (-6.2088, 106.8456),   // 雅加达
                (-7.2575, 112.7521),   // 泗水
                (-6.9175, 107.6191)    // 万隆
            },
            ["MY"] = new[]
            {
                (3.1390, 101.6869),    // 吉隆坡
                (5.4164, 100.3327),    // 槟城
                (1.4927, 103.7414)     // 新山
            },
            ["SG"] = new[]
            {
                (1.3521, 103.8198),    // 新加坡市中心
                (1.3966, 103.7764),    // 裕廊
                (1.4382, 103.7890)     // 兀兰
            },
            ["PH"] = new[]
            {
                (14.5995, 120.9842),   // 马尼拉
                (10.3157, 123.8854),   // 宿务
                (16.4023, 120.5960)    // 碧瑶
            },
            ["HK"] = new[]
            {
                (22.3193, 114.1694),   // 香港岛
                (22.3964, 114.1095),   // 九龙
                (22.4818, 114.1747)    // 新界
            },
            ["TW"] = new[]
            {
                (25.0330, 121.5654),   // 台北
                (22.6273, 120.3014),   // 高雄
                (24.1477, 120.6736)    // 台中
            },

            // 欧洲其他国家
            ["PT"] = new[]
            {
                (38.7223, -9.1393),    // 里斯本
                (41.1579, -8.6291),    // 波尔图
                (32.6669, -16.9241)    // 马德拉
            },
            ["NL"] = new[]
            {
                (52.3676, 4.9041),     // 阿姆斯特丹
                (51.9225, 4.47917),    // 鹿特丹
                (52.0907, 5.1214)      // 乌得勒支
            },
            ["BE"] = new[]
            {
                (50.8503, 4.3517),     // 布鲁塞尔
                (51.2194, 4.4025),     // 安特卫普
                (50.8263, 3.2633)      // 根特
            },
            ["CH"] = new[]
            {
                (47.3769, 8.5417),     // 苏黎世
                (46.9481, 7.4474),     // 伯尔尼
                (46.2044, 6.1432)      // 日内瓦
            },

            // 俄罗斯
            ["RU"] = new[]
            {
                (55.7558, 37.6176),    // 莫斯科
                (59.9311, 30.3609),    // 圣彼得堡
                (55.0084, 82.9357),    // 新西伯利亚
                (56.8431, 60.6454),    // 叶卡捷琳堡
                (55.1644, 61.4368)     // 车里雅宾斯克
            },

            // 中东
            ["SA"] = new[]
            {
                (24.7136, 46.6753),    // 利雅得
                (21.3891, 39.8579),    // 吉达
                (26.3927, 49.9777)     // 达曼
            },
            ["AE"] = new[]
            {
                (25.2048, 55.2708),    // 迪拜
                (24.4539, 54.3773),    // 阿布扎比
                (25.3463, 55.4209)     // 沙迦
            },
            ["IL"] = new[]
            {
                (31.7683, 35.2137),    // 耶路撒冷
                (32.0853, 34.7818),    // 特拉维夫
                (32.7940, 34.9896)     // 海法
            },
            ["EG"] = new[]
            {
                (30.0444, 31.2357),    // 开罗
                (31.2001, 29.9187),    // 亚历山大
                (25.6872, 32.6396)     // 卢克索
            },

            // 非洲
            ["ZA"] = new[]
            {
                (-26.2041, 28.0473),   // 约翰内斯堡
                (-33.9249, 18.4241),   // 开普敦
                (-29.8587, 31.0218)    // 德班
            },
            ["MA"] = new[]
            {
                (33.9716, -6.8498),    // 拉巴特
                (33.5731, -7.5898),    // 卡萨布兰卡
                (31.6295, -7.9811)     // 马拉喀什
            },

            // 其他国家使用单一位置
            ["TN"] = new[] { (33.8869, 9.5375) },      // 突尼斯
            ["DZ"] = new[] { (28.0339, 1.6596) },      // 阿尔及利亚
            ["LY"] = new[] { (26.3351, 17.2283) },     // 利比亚
            ["AT"] = new[] { (47.5162, 14.5501) },     // 奥地利
            ["SE"] = new[] { (60.1282, 18.6435) },     // 瑞典
            ["NO"] = new[] { (60.4720, 8.4689) },      // 挪威
            ["DK"] = new[] { (56.2639, 9.5018) },      // 丹麦
            ["FI"] = new[] { (61.9241, 25.7482) },     // 芬兰
            ["PL"] = new[] { (51.9194, 19.1451) },     // 波兰
            ["CZ"] = new[] { (49.8175, 15.4730) },     // 捷克
            ["HU"] = new[] { (47.1625, 19.5033) },     // 匈牙利
            ["RO"] = new[] { (45.9432, 24.9668) },     // 罗马尼亚
            ["BG"] = new[] { (42.7339, 25.4858) },     // 保加利亚
            ["GR"] = new[] { (39.0742, 21.8243) },     // 希腊
            ["UA"] = new[] { (48.3794, 31.1656) },     // 乌克兰
            ["TR"] = new[] { (38.9637, 35.2433) },     // 土耳其
            ["MX"] = new[] { (19.4326, -99.1332) },    // 墨西哥
            ["CO"] = new[] { (4.5709, -74.2973) },     // 哥伦比亚
            ["PE"] = new[] { (-9.1900, -75.0152) },    // 秘鲁
            ["VE"] = new[] { (6.4238, -66.5897) },     // 委内瑞拉
            ["EC"] = new[] { (-1.8312, -78.1834) },    // 厄瓜多尔
            ["UY"] = new[] { (-32.5228, -55.7658) },   // 乌拉圭
            ["IR"] = new[] { (32.4279, 53.6880) },     // 伊朗
            ["JO"] = new[] { (30.5852, 36.2384) },     // 约旦
            ["LB"] = new[] { (33.8547, 35.8623) },     // 黎巴嫩
            ["KW"] = new[] { (29.3117, 47.4818) },     // 科威特
            ["QA"] = new[] { (25.3548, 51.1839) },     // 卡塔尔
            ["BH"] = new[] { (25.9304, 50.6378) },     // 巴林
            ["OM"] = new[] { (21.4735, 55.9754) }      // 阿曼
        };

        /// <summary>
        /// 根据国家代码获取对应的语言代码
        /// </summary>
        /// <param name="countryCode">国家代码（如US、CN、JP等）</param>
        /// <returns>语言代码，如果找不到则返回"en-US"作为默认值</returns>
        public static string GetLanguageCode(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return "en-US";

            var code = countryCode.ToUpper().Trim();
            return CountryToLanguageMapping.TryGetValue(code, out var languageCode) ? languageCode : "en-US";
        }

        /// <summary>
        /// 根据国家代码获取Chrome浏览器的语言参数
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>Chrome语言参数</returns>
        public static string GetChromeLanguageArg(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            return $"--lang={languageCode}";
        }

        /// <summary>
        /// 根据国家代码获取Accept-Language头部值
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>Accept-Language头部值</returns>
        public static string GetAcceptLanguageHeader(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            
            // 构建Accept-Language头部，包含主语言和备选语言
            var primaryLang = languageCode.Split('-')[0];
            if (primaryLang == "en")
            {
                return $"{languageCode},en;q=0.9";
            }
            else
            {
                return $"{languageCode},{primaryLang};q=0.9,en;q=0.8";
            }
        }

        /// <summary>
        /// 检查国家代码是否有对应的语言映射
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>如果有映射返回true，否则返回false</returns>
        public static bool HasLanguageMapping(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return false;

            return CountryToLanguageMapping.ContainsKey(countryCode.ToUpper().Trim());
        }

        /// <summary>
        /// 获取所有支持的国家代码和对应的语言代码
        /// </summary>
        /// <returns>国家代码和语言代码的字典</returns>
        public static Dictionary<string, string> GetAllLanguageMappings()
        {
            return new Dictionary<string, string>(CountryToLanguageMapping);
        }

        /// <summary>
        /// 根据国家代码获取对应的时区
        /// </summary>
        /// <param name="countryCode">国家代码（如US、CN、JP等）</param>
        /// <returns>时区字符串，如果找不到则返回"America/New_York"作为默认值</returns>
        public static string GetTimezone(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return "America/New_York";

            var code = countryCode.ToUpper().Trim();
            if (CountryToTimezoneMapping.TryGetValue(code, out var timezones))
            {
                // 如果有多个时区，随机选择一个
                var random = new Random();
                return timezones[random.Next(timezones.Length)];
            }

            return "America/New_York"; // 默认时区
        }

        /// <summary>
        /// 根据国家代码获取所有可能的时区
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>时区数组，如果找不到则返回默认时区数组</returns>
        public static string[] GetAllTimezones(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return new[] { "America/New_York" };

            var code = countryCode.ToUpper().Trim();
            return CountryToTimezoneMapping.TryGetValue(code, out var timezones)
                ? timezones
                : new[] { "America/New_York" };
        }

        /// <summary>
        /// 检查国家代码是否有对应的时区映射
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>如果有映射返回true，否则返回false</returns>
        public static bool HasTimezoneMapping(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return false;

            return CountryToTimezoneMapping.ContainsKey(countryCode.ToUpper().Trim());
        }

        /// <summary>
        /// 根据国家代码获取对应的地理位置（随机选择该国家的一个城市）
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>地理位置（纬度，经度），如果找不到则返回null</returns>
        public static (double Latitude, double Longitude)? GetGeolocation(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return null;

            var code = countryCode.ToUpper().Trim();
            if (CountryToGeolocationMapping.TryGetValue(code, out var locations))
            {
                // 从该国家的多个城市中随机选择一个
                var random = new Random();
                return locations[random.Next(locations.Length)];
            }

            return null;
        }

        /// <summary>
        /// 根据国家代码获取所有可能的地理位置
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>地理位置数组，如果找不到则返回空数组</returns>
        public static (double Latitude, double Longitude)[] GetAllGeolocations(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return Array.Empty<(double, double)>();

            var code = countryCode.ToUpper().Trim();
            return CountryToGeolocationMapping.TryGetValue(code, out var locations)
                ? locations
                : Array.Empty<(double, double)>();
        }

        /// <summary>
        /// 检查国家代码是否有对应的地理位置映射
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>如果有映射返回true，否则返回false</returns>
        public static bool HasGeolocationMapping(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return false;

            return CountryToGeolocationMapping.ContainsKey(countryCode.ToUpper().Trim());
        }

        /// <summary>
        /// 根据国家代码获取语言的显示名称
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>语言显示名称</returns>
        public static string GetLanguageDisplayName(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            
            return languageCode switch
            {
                "en-US" => "English (United States)",
                "en-GB" => "English (United Kingdom)",
                "zh-CN" => "中文 (简体)",
                "zh-TW" => "中文 (繁體)",
                "ja-JP" => "日本語",
                "ko-KR" => "한국어",
                "es-ES" => "Español (España)",
                "es-MX" => "Español (México)",
                "fr-FR" => "Français",
                "de-DE" => "Deutsch",
                "it-IT" => "Italiano",
                "pt-BR" => "Português (Brasil)",
                "pt-PT" => "Português (Portugal)",
                "ru-RU" => "Русский",
                "ar-SA" => "العربية",
                "th-TH" => "ไทย",
                "tr-TR" => "Türkçe",
                "hi-IN" => "हिन्दी",
                "vi-VN" => "Tiếng Việt",
                "id-ID" => "Bahasa Indonesia",
                "ms-MY" => "Bahasa Melayu",
                _ => languageCode
            };
        }
    }
}
