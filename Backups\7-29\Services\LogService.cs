using System;
using System.IO;
using System.Threading;

namespace AWSAutoRegister.Services
{
    public class LogService
    {
        private readonly string _logFilePath;
        private readonly object _lockObject = new object();
        private static LogService? _instance;
        private static readonly object _instanceLock = new object();

        private LogService()
        {
            // 获取程序根目录
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _logFilePath = Path.Combine(appDirectory, "awstool_log.txt");
        }

        public static LogService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_instanceLock)
                    {
                        if (_instance == null)
                        {
                            _instance = new LogService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 记录按钮点击操作
        /// </summary>
        /// <param name="buttonName">按钮名称</param>
        /// <param name="action">触发的操作</param>
        /// <param name="threadId">线程ID（可选）</param>
        public void LogButtonClick(string buttonName, string action, int? threadId = null)
        {
            var logEntry = $"[按钮操作] {buttonName} -> {action}";
            WriteLog(logEntry, threadId);
        }

        /// <summary>
        /// 记录系统状态变化
        /// </summary>
        /// <param name="status">状态信息</param>
        /// <param name="threadId">线程ID（可选）</param>
        public void LogStatusChange(string status, int? threadId = null)
        {
            var logEntry = threadId.HasValue ?
                $"[系统状态] {status}" :
                $"[系统状态] {status}";
            WriteLog(logEntry, threadId);
        }

        /// <summary>
        /// 记录注册开始（空一行分隔）
        /// </summary>
        /// <param name="dataInfo">数据信息</param>
        /// <param name="threadId">线程ID（可选）</param>
        public void LogRegistrationStart(string dataInfo, int? threadId = null)
        {
            WriteLog("", threadId); // 空行分隔
            var logEntry = $"[注册开始] {dataInfo}";
            WriteLog(logEntry, threadId);
        }

        /// <summary>
        /// 记录注册结束
        /// </summary>
        /// <param name="result">结果信息</param>
        /// <param name="threadId">线程ID（可选）</param>
        public void LogRegistrationEnd(string result, int? threadId = null)
        {
            var logEntry = $"[注册结束] {result}";
            WriteLog(logEntry, threadId);
        }

        /// <summary>
        /// 记录一般信息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="threadId">线程ID（可选）</param>
        public void LogInfo(string message, int? threadId = null)
        {
            var logEntry = $"[信息] {message}";
            WriteLog(logEntry, threadId);
        }

        /// <summary>
        /// 记录错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        /// <param name="threadId">线程ID（可选）</param>
        public void LogError(string error, int? threadId = null)
        {
            var logEntry = $"[错误] {error}";
            WriteLog(logEntry, threadId);
        }

        /// <summary>
        /// 记录警告信息
        /// </summary>
        /// <param name="warning">警告信息</param>
        /// <param name="threadId">线程ID（可选）</param>
        public void LogWarning(string warning, int? threadId = null)
        {
            var logEntry = $"[警告] {warning}";
            WriteLog(logEntry, threadId);
        }

        /// <summary>
        /// 记录警告信息
        /// </summary>
        /// <param name="warning">警告信息</param>
        public void LogWarning(string warning)
        {
            var logEntry = $"[警告] {warning}";
            WriteLog(logEntry);
        }

        /// <summary>
        /// 写入日志到文件
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="threadId">线程ID（可选）</param>
        private void WriteLog(string message, int? threadId = null)
        {
            lock (_lockObject)
            {
                try
                {
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    string logLine;

                    if (string.IsNullOrEmpty(message))
                    {
                        logLine = "";
                    }
                    else if (threadId.HasValue)
                    {
                        logLine = $"{timestamp} 线程{threadId}：{message}";
                    }
                    else
                    {
                        logLine = $"{timestamp} {message}";
                    }

                    File.AppendAllText(_logFilePath, logLine + Environment.NewLine);
                }
                catch (Exception ex)
                {
                    // 如果日志写入失败，不要抛出异常，避免影响主程序
                    System.Diagnostics.Debug.WriteLine($"日志写入失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 清空日志文件
        /// </summary>
        public void ClearLog()
        {
            lock (_lockObject)
            {
                try
                {
                    if (File.Exists(_logFilePath))
                    {
                        File.WriteAllText(_logFilePath, "");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"清空日志失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取日志文件路径
        /// </summary>
        public string GetLogFilePath()
        {
            return _logFilePath;
        }
    }
}
