using AWSAutoRegister.Models;
using System.IO;

namespace AWSAutoRegister.Services
{
    public class FileService
    {
        public List<RegistrationData> LoadDataFromFile(string filePath)
        {
            var dataList = new List<RegistrationData>();

            if (!File.Exists(filePath))
                throw new FileNotFoundException("文件不存在");

            var lines = File.ReadAllLines(filePath);
            var dataLines = new List<string>();
            var terminatedDataStartIndex = -1;
            var failedDataStartIndex = -1;
            var successDataStartIndex = -1;

            // 查找"手动终止："标记
            for (int i = 0; i < lines.Length; i++)
            {
                if (lines[i].Trim() == "手动终止：")
                {
                    terminatedDataStartIndex = i;
                    break;
                }
            }

            // 查找"注册失败："和"注册失败数据："标记
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (line == "注册失败：" || line == "注册失败数据：")
                {
                    failedDataStartIndex = i;
                    break;
                }
            }

            // 查找"注册成功："标记
            for (int i = 0; i < lines.Length; i++)
            {
                if (lines[i].Trim() == "注册成功：")
                {
                    successDataStartIndex = i;
                    break;
                }
            }

            // 查找其他完成数据标记
            var completedDataMarkers = new[]
            {
                "成功数据：",
                "提示账单账户："
            };

            var completedDataStartIndex = -1;
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (completedDataMarkers.Any(marker => line == marker))
                {
                    if (completedDataStartIndex == -1 || i < completedDataStartIndex)
                    {
                        completedDataStartIndex = i;
                    }
                }
            }

            // 确定数据读取的结束位置：取所有标记中最早出现的位置
            var endIndex = lines.Length;
            var excludeIndices = new List<int>();

            if (terminatedDataStartIndex != -1) excludeIndices.Add(terminatedDataStartIndex);
            if (failedDataStartIndex != -1) excludeIndices.Add(failedDataStartIndex);
            if (successDataStartIndex != -1) excludeIndices.Add(successDataStartIndex);
            if (completedDataStartIndex != -1) excludeIndices.Add(completedDataStartIndex);

            if (excludeIndices.Count > 0)
            {
                endIndex = excludeIndices.Min();
            }
            
            for (int i = 0; i < endIndex; i++)
            {
                var line = lines[i].Trim();
                if (!string.IsNullOrEmpty(line) && line.Contains('|'))
                {
                    try
                    {
                        var data = RegistrationData.FromLine(line);
                        // 新加载的数据默认为"未处理"状态
                        data.Status = Models.DataProcessStatus.Unprocessed;
                        dataList.Add(data);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"第{i + 1}行数据格式错误: {ex.Message}");
                    }
                }
            }

            return dataList;
        }

        public void MoveDataToUsed(string filePath, RegistrationData dataToMove)
        {
            try
            {
                if (!File.Exists(filePath))
                    return;

                var lines = File.ReadAllLines(filePath).ToList();
                var dataToMoveString = dataToMove.ToLine();

                // 查找并移除要移动的数据行
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == dataToMoveString)
                    {
                        lines.RemoveAt(i);
                        break;
                    }
                }

                // 查找"已被使用数据："标记
                var usedDataIndex = -1;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == "已被使用数据：")
                    {
                        usedDataIndex = i;
                        break;
                    }
                }

                // 如果没有找到标记，添加标记
                if (usedDataIndex == -1)
                {
                    lines.Add("");
                    lines.Add("已被使用数据：");
                    usedDataIndex = lines.Count - 1;
                }

                // 在已使用数据区域添加数据
                lines.Insert(usedDataIndex + 1, dataToMoveString);

                // 写回文件
                File.WriteAllLines(filePath, lines);
            }
            catch
            {
                // 忽略文件操作错误
            }
        }

        public void MoveUsedData(string filePath, RegistrationData usedData)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var lines = File.ReadAllLines(filePath).ToList();
                var dataLine = usedData.ToLine();

                // 记录操作信息
                Console.WriteLine($"[FileService] 移动数据到已使用区域: {usedData.Email}");
                Console.WriteLine($"[FileService] 数据行: {dataLine}");

                // 移除原数据行
                bool dataFound = false;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == dataLine)
                    {
                        lines.RemoveAt(i);
                        dataFound = true;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到并移除原数据");
                        break;
                    }
                }

                if (!dataFound)
                {
                    Console.WriteLine($"[FileService] 警告：未找到原数据行，继续添加到已使用区域");
                }

                // 查找或创建"已被使用数据："标记
                var usedDataIndex = -1;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == "已被使用数据：")
                    {
                        usedDataIndex = i;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到已使用数据标记");
                        break;
                    }
                }

                if (usedDataIndex == -1)
                {
                    // 如果没有找到标记，在文件末尾添加
                    lines.Add("");
                    lines.Add("已被使用数据：");
                    lines.Add(dataLine);
                    Console.WriteLine($"[FileService] 创建新的已使用数据区域并添加数据");
                }
                else
                {
                    // 在标记后添加数据
                    lines.Insert(usedDataIndex + 1, dataLine);
                    Console.WriteLine($"[FileService] 在已使用数据区域添加数据");
                }

                File.WriteAllLines(filePath, lines);
                Console.WriteLine($"[FileService] 成功保存文件，共{lines.Count}行");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FileService] 移动已使用数据失败: {ex.Message}");
                throw new Exception($"移动已使用数据失败: {ex.Message}");
            }
        }

        public void MoveSuccessData(string filePath, RegistrationData successData)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var lines = File.ReadAllLines(filePath).ToList();
                var dataLine = successData.ToLine();

                // 记录操作信息
                Console.WriteLine($"[FileService] 移动数据到注册成功区域: {successData.Email}");
                Console.WriteLine($"[FileService] 数据行: {dataLine}");

                // 移除原数据行
                bool dataFound = false;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == dataLine)
                    {
                        lines.RemoveAt(i);
                        dataFound = true;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到并移除原数据");
                        break;
                    }
                }

                if (!dataFound)
                {
                    Console.WriteLine($"[FileService] 警告：未找到原数据行，继续添加到注册成功区域");
                }

                // 查找或创建"注册成功："标记
                var successDataIndex = -1;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == "注册成功：")
                    {
                        successDataIndex = i;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到注册成功标记");
                        break;
                    }
                }

                if (successDataIndex == -1)
                {
                    // 如果没有找到标记，在文件末尾添加
                    lines.Add("");
                    lines.Add("注册成功：");
                    lines.Add(dataLine);
                    Console.WriteLine($"[FileService] 创建新的注册成功区域并添加数据");
                }
                else
                {
                    // 在标记后添加数据
                    lines.Insert(successDataIndex + 1, dataLine);
                    Console.WriteLine($"[FileService] 在注册成功区域添加数据");
                }

                File.WriteAllLines(filePath, lines);
                Console.WriteLine($"[FileService] 成功保存文件，共{lines.Count}行");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FileService] 移动注册成功数据失败: {ex.Message}");
                throw new Exception($"移动注册成功数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移动账单问题数据到注册成功区域（添加账单标记）
        /// </summary>
        public void MoveBillingIssueData(string filePath, RegistrationData successData)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var lines = File.ReadAllLines(filePath).ToList();
                var dataLine = successData.ToLine();

                // 为账单问题数据添加特殊标记
                var billingDataLine = dataLine + "   //账单无法取密钥";

                // 记录操作信息
                Console.WriteLine($"[FileService] 移动账单问题数据到注册成功区域: {successData.Email}");
                Console.WriteLine($"[FileService] 数据行: {billingDataLine}");

                // 移除原数据行（包括可能已有账单标记的行）
                bool dataFound = false;
                for (int i = 0; i < lines.Count; i++)
                {
                    var currentLine = lines[i].Trim();
                    // 检查是否为原数据行或已有账单标记的数据行
                    if (currentLine == dataLine || currentLine == billingDataLine)
                    {
                        lines.RemoveAt(i);
                        dataFound = true;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到并移除原数据（可能已有账单标记）");
                        break;
                    }
                }

                if (!dataFound)
                {
                    Console.WriteLine($"[FileService] 警告：未找到原数据行，继续添加到注册成功区域");
                }

                // 查找或创建"注册成功："标记
                var successDataIndex = -1;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == "注册成功：")
                    {
                        successDataIndex = i;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到注册成功标记");
                        break;
                    }
                }

                // 检查注册成功区域是否已存在相同的账单数据，避免重复添加
                bool billingDataExists = false;
                if (successDataIndex != -1)
                {
                    for (int i = successDataIndex + 1; i < lines.Count; i++)
                    {
                        if (lines[i].Trim() == billingDataLine.Trim())
                        {
                            billingDataExists = true;
                            Console.WriteLine($"[FileService] 账单数据已存在于注册成功区域第{i+1}行，跳过重复添加");
                            break;
                        }
                        // 如果遇到其他区域标记，停止检查
                        if (lines[i].Trim().EndsWith("：") && lines[i].Trim() != "注册成功：")
                        {
                            break;
                        }
                    }
                }

                if (billingDataExists)
                {
                    Console.WriteLine($"[FileService] 跳过重复的账单数据添加");
                    return;
                }

                if (successDataIndex == -1)
                {
                    // 如果没有找到标记，在文件末尾添加
                    lines.Add("");
                    lines.Add("注册成功：");
                    lines.Add(billingDataLine);
                    Console.WriteLine($"[FileService] 创建新的注册成功区域并添加账单问题数据");
                }
                else
                {
                    // 在标记后添加数据
                    lines.Insert(successDataIndex + 1, billingDataLine);
                    Console.WriteLine($"[FileService] 在注册成功区域添加账单问题数据");
                }

                File.WriteAllLines(filePath, lines);
                Console.WriteLine($"[FileService] 成功保存账单问题数据文件，共{lines.Count}行");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FileService] 移动账单问题数据失败: {ex.Message}");
                throw new Exception($"移动账单问题数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移动无资格问题数据到注册成功区域（添加无资格标记）
        /// </summary>
        public void MoveIneligibleIssueData(string filePath, RegistrationData successData)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var lines = File.ReadAllLines(filePath).ToList();
                var dataLine = successData.ToLine();

                // 为无资格问题数据添加特殊标记
                var ineligibleDataLine = dataLine + "   //无资格";

                // 记录操作信息
                Console.WriteLine($"[FileService] 移动无资格问题数据到注册成功区域: {successData.Email}");
                Console.WriteLine($"[FileService] 数据行: {ineligibleDataLine}");

                // 移除原数据行（包括可能已有无资格标记的行）
                bool dataFound = false;
                for (int i = 0; i < lines.Count; i++)
                {
                    var currentLine = lines[i].Trim();
                    // 检查是否为原数据行或已有无资格标记的数据行
                    if (currentLine == dataLine || currentLine == ineligibleDataLine)
                    {
                        lines.RemoveAt(i);
                        dataFound = true;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到并移除原数据（可能已有无资格标记）");
                        break;
                    }
                }

                if (!dataFound)
                {
                    Console.WriteLine($"[FileService] 警告：未找到原数据行，继续添加到注册成功区域");
                }

                // 查找或创建"注册成功："标记
                var successDataIndex = -1;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == "注册成功：")
                    {
                        successDataIndex = i;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到注册成功标记");
                        break;
                    }
                }

                // 检查注册成功区域是否已存在相同的无资格数据，避免重复添加
                bool ineligibleDataExists = false;
                if (successDataIndex != -1)
                {
                    for (int i = successDataIndex + 1; i < lines.Count; i++)
                    {
                        if (lines[i].Trim() == ineligibleDataLine.Trim())
                        {
                            ineligibleDataExists = true;
                            Console.WriteLine($"[FileService] 无资格数据已存在于注册成功区域第{i+1}行，跳过重复添加");
                            break;
                        }
                        // 如果遇到其他区域标记，停止检查
                        if (lines[i].Trim().EndsWith("：") && lines[i].Trim() != "注册成功：")
                        {
                            break;
                        }
                    }
                }

                if (ineligibleDataExists)
                {
                    Console.WriteLine($"[FileService] 跳过重复的无资格数据添加");
                    return;
                }

                if (successDataIndex == -1)
                {
                    // 如果没有找到标记，在文件末尾添加
                    lines.Add("");
                    lines.Add("注册成功：");
                    lines.Add(ineligibleDataLine);
                    Console.WriteLine($"[FileService] 创建新的注册成功区域并添加无资格问题数据");
                }
                else
                {
                    // 在标记后添加数据
                    lines.Insert(successDataIndex + 1, ineligibleDataLine);
                    Console.WriteLine($"[FileService] 在注册成功区域添加无资格问题数据");
                }

                File.WriteAllLines(filePath, lines);
                Console.WriteLine($"[FileService] 成功保存无资格问题数据文件，共{lines.Count}行");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FileService] 移动无资格问题数据失败: {ex.Message}");
                throw new Exception($"移动无资格问题数据失败: {ex.Message}");
            }
        }

        public void MoveTerminatedData(string filePath, RegistrationData terminatedData)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var lines = File.ReadAllLines(filePath).ToList();
                var dataLine = terminatedData.ToLine();

                // 记录操作信息
                Console.WriteLine($"[FileService] 移动数据到终止注册区域: {terminatedData.Email}");
                Console.WriteLine($"[FileService] 数据行: {dataLine}");

                // 移除原数据行 - 使用邮箱地址匹配，更加可靠
                bool dataFound = false;
                for (int i = 0; i < lines.Count; i++)
                {
                    var currentLine = lines[i].Trim();

                    // 跳过空行和标记行
                    if (string.IsNullOrEmpty(currentLine) ||
                        currentLine.EndsWith("：") ||
                        currentLine.StartsWith("//") ||
                        currentLine.Contains("注册成功:") ||
                        currentLine.Contains("注册失败：") ||
                        currentLine.Contains("手动终止：") ||
                        currentLine.Contains("成功数据："))
                    {
                        continue;
                    }

                    // 尝试解析当前行，通过邮箱地址匹配
                    try
                    {
                        var fields = currentLine.Split('|');
                        if (fields.Length > 0 && fields[0].Trim().Equals(terminatedData.Email, StringComparison.OrdinalIgnoreCase))
                        {
                            lines.RemoveAt(i);
                            dataFound = true;
                            Console.WriteLine($"[FileService] 在第{i+1}行通过邮箱匹配找到并移除原数据: {terminatedData.Email}");
                            Console.WriteLine($"[FileService] 移除的数据行: {currentLine}");
                            break;
                        }
                    }
                    catch
                    {
                        // 如果解析失败，尝试精确匹配作为备选方案
                        if (currentLine == dataLine)
                        {
                            lines.RemoveAt(i);
                            dataFound = true;
                            Console.WriteLine($"[FileService] 在第{i+1}行通过精确匹配找到并移除原数据");
                            break;
                        }
                    }
                }

                if (!dataFound)
                {
                    Console.WriteLine($"[FileService] 警告：未找到邮箱为 {terminatedData.Email} 的原数据行，继续添加到终止注册区域");
                    Console.WriteLine($"[FileService] 查找的数据行格式: {dataLine}");
                }

                // 查找或创建"手动终止："标记
                var terminatedDataIndex = -1;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == "手动终止：")
                    {
                        terminatedDataIndex = i;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到终止注册数据标记");
                        break;
                    }
                }

                if (terminatedDataIndex == -1)
                {
                    // 如果没有找到标记，在文件末尾添加
                    lines.Add("");
                    lines.Add("手动终止：");
                    lines.Add(dataLine);
                    Console.WriteLine($"[FileService] 创建新的终止注册数据区域并添加数据");
                }
                else
                {
                    // 在标记后添加数据
                    lines.Insert(terminatedDataIndex + 1, dataLine);
                    Console.WriteLine($"[FileService] 在终止注册数据区域添加数据");
                }

                File.WriteAllLines(filePath, lines);
                Console.WriteLine($"[FileService] 成功保存文件，共{lines.Count}行");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FileService] 移动终止数据失败: {ex.Message}");
                throw new Exception($"移动终止数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存剪贴板信息到"成功数据："区域
        /// </summary>
        public void SaveClipboardInfoToSuccessData(string filePath, string clipboardContent)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var lines = File.ReadAllLines(filePath).ToList();

                // 记录操作信息
                Console.WriteLine($"[FileService] 保存剪贴板信息到成功数据区域");
                Console.WriteLine($"[FileService] 剪贴板内容: {clipboardContent}");

                // 验证剪贴板内容格式，防止失败数据格式被错误写入成功数据区域
                if (clipboardContent.Contains("// 失败原因:"))
                {
                    Console.WriteLine($"[FileService] 检测到失败数据格式，拒绝写入成功数据区域: {clipboardContent}");
                    throw new InvalidOperationException($"拒绝将失败数据格式写入成功数据区域: {clipboardContent}");
                }

                // 检查是否已存在相同的剪贴板内容，避免重复添加
                bool contentExists = false;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == clipboardContent.Trim())
                    {
                        contentExists = true;
                        Console.WriteLine($"[FileService] 剪贴板内容已存在于第{i+1}行，跳过重复添加");
                        break;
                    }
                }

                if (contentExists)
                {
                    Console.WriteLine($"[FileService] 跳过重复的剪贴板内容保存");
                    return;
                }

                // 查找或创建"成功数据："标记
                var successDataIndex = -1;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == "成功数据：")
                    {
                        successDataIndex = i;
                        Console.WriteLine($"[FileService] 在第{i+1}行找到成功数据标记");
                        break;
                    }
                }

                if (successDataIndex == -1)
                {
                    // 如果没有找到标记，在文件末尾添加
                    lines.Add("");
                    lines.Add("成功数据：");
                    lines.Add(clipboardContent);
                    Console.WriteLine($"[FileService] 创建新的成功数据区域并添加剪贴板信息");
                }
                else
                {
                    // 在标记后添加数据
                    lines.Insert(successDataIndex + 1, clipboardContent);
                    Console.WriteLine($"[FileService] 在成功数据区域添加剪贴板信息");
                }

                File.WriteAllLines(filePath, lines);
                Console.WriteLine($"[FileService] 成功保存文件，共{lines.Count}行");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FileService] 保存剪贴板信息到成功数据区域失败: {ex.Message}");
                throw new Exception($"保存剪贴板信息到成功数据区域失败: {ex.Message}");
            }
        }
    }
}
