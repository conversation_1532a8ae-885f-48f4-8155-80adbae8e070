using System.Collections.Generic;

namespace AWSAutoRegister.Services
{
    public class CountryCodeService
    {
        public class CountryInfo
        {
            public string Code { get; set; } = "";
            public string Name { get; set; } = "";
            public string PhoneCode { get; set; } = "";
            public string ApiCode { get; set; } = "";
        }

        public static List<CountryInfo> GetSupportedCountries()
        {
            return new List<CountryInfo>
            {
                // 原有国家（保持不变）
                new CountryInfo { Code = "th", Name = "泰国 (Thailand) +66", PhoneCode = "+66", ApiCode = "th" },
                new CountryInfo { Code = "tr", Name = "土耳其 (Turkey) +90", PhoneCode = "+90", ApiCode = "tr" },
                new CountryInfo { Code = "mx", Name = "墨西哥 (Mexico) +52", PhoneCode = "+52", ApiCode = "mx" },
                new CountryInfo { Code = "ve", Name = "委内瑞拉 (Venezuela) +58", PhoneCode = "+58", ApiCode = "ve" },
                new CountryInfo { Code = "za", Name = "南非 (South Africa) +27", PhoneCode = "+27", ApiCode = "za" },
                new CountryInfo { Code = "us", Name = "美国 (United States) +1", PhoneCode = "+1", ApiCode = "us" },
                new CountryInfo { Code = "gb", Name = "英国 (United Kingdom) +44", PhoneCode = "+44", ApiCode = "gb" },
                new CountryInfo { Code = "ca", Name = "加拿大 (Canada) +1", PhoneCode = "+1", ApiCode = "ca" },
                new CountryInfo { Code = "au", Name = "澳大利亚 (Australia) +61", PhoneCode = "+61", ApiCode = "au" },
                new CountryInfo { Code = "de", Name = "德国 (Germany) +49", PhoneCode = "+49", ApiCode = "de" },
                new CountryInfo { Code = "fr", Name = "法国 (France) +33", PhoneCode = "+33", ApiCode = "fr" },
                new CountryInfo { Code = "jp", Name = "日本 (Japan) +81", PhoneCode = "+81", ApiCode = "jp" },
                new CountryInfo { Code = "kr", Name = "韩国 (South Korea) +82", PhoneCode = "+82", ApiCode = "kr" },
                new CountryInfo { Code = "sg", Name = "新加坡 (Singapore) +65", PhoneCode = "+65", ApiCode = "sg" },
                new CountryInfo { Code = "my", Name = "马来西亚 (Malaysia) +60", PhoneCode = "+60", ApiCode = "my" },
                new CountryInfo { Code = "ph", Name = "菲律宾 (Philippines) +63", PhoneCode = "+63", ApiCode = "ph" },
                new CountryInfo { Code = "vn", Name = "越南 (Vietnam) +84", PhoneCode = "+84", ApiCode = "vn" },
                new CountryInfo { Code = "id", Name = "印度尼西亚 (Indonesia) +62", PhoneCode = "+62", ApiCode = "id" },
                new CountryInfo { Code = "in", Name = "印度 (India) +91", PhoneCode = "+91", ApiCode = "in" },
                new CountryInfo { Code = "cn", Name = "中国 (China) +86", PhoneCode = "+86", ApiCode = "cn" },
                new CountryInfo { Code = "hk", Name = "中国香港 (Hong Kong) +852", PhoneCode = "+852", ApiCode = "hk" },
                new CountryInfo { Code = "tw", Name = "中国台湾 (Taiwan) +886", PhoneCode = "+886", ApiCode = "tw" },
                new CountryInfo { Code = "ru", Name = "俄罗斯 (Russia) +7", PhoneCode = "+7", ApiCode = "ru" },
                new CountryInfo { Code = "br", Name = "巴西 (Brazil) +55", PhoneCode = "+55", ApiCode = "br" },
                new CountryInfo { Code = "ar", Name = "阿根廷 (Argentina) +54", PhoneCode = "+54", ApiCode = "ar" },
                new CountryInfo { Code = "cl", Name = "智利 (Chile) +56", PhoneCode = "+56", ApiCode = "cl" },
                new CountryInfo { Code = "co", Name = "哥伦比亚 (Colombia) +57", PhoneCode = "+57", ApiCode = "co" },
                new CountryInfo { Code = "pe", Name = "秘鲁 (Peru) +51", PhoneCode = "+51", ApiCode = "pe" },
                new CountryInfo { Code = "bo", Name = "玻利维亚 (Bolivia) +591", PhoneCode = "+591", ApiCode = "bo" },
                new CountryInfo { Code = "ec", Name = "厄瓜多尔 (Ecuador) +593", PhoneCode = "+593", ApiCode = "ec" },
                new CountryInfo { Code = "py", Name = "巴拉圭 (Paraguay) +595", PhoneCode = "+595", ApiCode = "py" },
                new CountryInfo { Code = "uy", Name = "乌拉圭 (Uruguay) +598", PhoneCode = "+598", ApiCode = "uy" },
                new CountryInfo { Code = "eg", Name = "埃及 (Egypt) +20", PhoneCode = "+20", ApiCode = "eg" },
                new CountryInfo { Code = "ng", Name = "尼日利亚 (Nigeria) +234", PhoneCode = "+234", ApiCode = "ng" },
                new CountryInfo { Code = "ke", Name = "肯尼亚 (Kenya) +254", PhoneCode = "+254", ApiCode = "ke" },
                new CountryInfo { Code = "ma", Name = "摩洛哥 (Morocco) +212", PhoneCode = "+212", ApiCode = "ma" },
                new CountryInfo { Code = "tn", Name = "突尼斯 (Tunisia) +216", PhoneCode = "+216", ApiCode = "tn" },
                new CountryInfo { Code = "dz", Name = "阿尔及利亚 (Algeria) +213", PhoneCode = "+213", ApiCode = "dz" },
                new CountryInfo { Code = "ly", Name = "利比亚 (Libya) +218", PhoneCode = "+218", ApiCode = "ly" },
                new CountryInfo { Code = "sd", Name = "苏丹 (Sudan) +249", PhoneCode = "+249", ApiCode = "sd" },
                new CountryInfo { Code = "et", Name = "埃塞俄比亚 (Ethiopia) +251", PhoneCode = "+251", ApiCode = "et" },
                new CountryInfo { Code = "ug", Name = "乌干达 (Uganda) +256", PhoneCode = "+256", ApiCode = "ug" },
                new CountryInfo { Code = "tz", Name = "坦桑尼亚 (Tanzania) +255", PhoneCode = "+255", ApiCode = "tz" },
                new CountryInfo { Code = "mz", Name = "莫桑比克 (Mozambique) +258", PhoneCode = "+258", ApiCode = "mz" },
                new CountryInfo { Code = "zw", Name = "津巴布韦 (Zimbabwe) +263", PhoneCode = "+263", ApiCode = "zw" },
                new CountryInfo { Code = "bw", Name = "博茨瓦纳 (Botswana) +267", PhoneCode = "+267", ApiCode = "bw" },
                new CountryInfo { Code = "zm", Name = "赞比亚 (Zambia) +260", PhoneCode = "+260", ApiCode = "zm" },
                new CountryInfo { Code = "mw", Name = "马拉维 (Malawi) +265", PhoneCode = "+265", ApiCode = "mw" },
                new CountryInfo { Code = "mg", Name = "马达加斯加 (Madagascar) +261", PhoneCode = "+261", ApiCode = "mg" },
                new CountryInfo { Code = "mu", Name = "毛里求斯 (Mauritius) +230", PhoneCode = "+230", ApiCode = "mu" },
                new CountryInfo { Code = "sc", Name = "塞舌尔 (Seychelles) +248", PhoneCode = "+248", ApiCode = "sc" },
                new CountryInfo { Code = "re", Name = "留尼汪 (Reunion) +262", PhoneCode = "+262", ApiCode = "re" },
                new CountryInfo { Code = "yt", Name = "马约特 (Mayotte) +262", PhoneCode = "+262", ApiCode = "yt" },
                // 新增缺失的国家（按字母顺序排列
                new CountryInfo { Code = "ad", Name = "安道尔 (Andorra) +376", PhoneCode = "+376", ApiCode = "ad" },
                new CountryInfo { Code = "ae", Name = "迪拜 (United Arab Emirates) +971", PhoneCode = "+971", ApiCode = "ae" },
                new CountryInfo { Code = "af", Name = "阿富汗 (Afghanistan) +93", PhoneCode = "+93", ApiCode = "af" },
                //new CountryInfo { Code = "ag", Name = "安提瓜和巴布达 (Antigua and Barbuda) +1268", PhoneCode = "+1268", ApiCode = "ag" },
                new CountryInfo { Code = "ai", Name = "安圭拉岛 (Anguilla) +1264", PhoneCode = "+1264", ApiCode = "ai" },
                new CountryInfo { Code = "al", Name = "阿尔巴尼亚 (Albania) +355", PhoneCode = "+355", ApiCode = "al" },
                new CountryInfo { Code = "am", Name = "亚美尼亚 (Armenia) +374", PhoneCode = "+374", ApiCode = "am" },
                new CountryInfo { Code = "ao", Name = "安哥拉 (Angola) +244", PhoneCode = "+244", ApiCode = "ao" },
                new CountryInfo { Code = "as", Name = "美属萨摩亚 (American Samoa) +1684", PhoneCode = "+1684", ApiCode = "as" },
                new CountryInfo { Code = "at", Name = "奥地利 (Austria) +43", PhoneCode = "+43", ApiCode = "at" },
                new CountryInfo { Code = "aw", Name = "阿鲁巴 (Aruba) +297", PhoneCode = "+297", ApiCode = "aw" },
                new CountryInfo { Code = "az", Name = "阿塞拜疆 (Azerbaijan) +994", PhoneCode = "+994", ApiCode = "az" },
                //new CountryInfo { Code = "ba", Name = "波斯尼亚和黑塞哥维那 (Bosnia and Herzegovina) +387", PhoneCode = "+387", ApiCode = "ba" },
                new CountryInfo { Code = "bb", Name = "巴巴多斯 (Barbados) +1246", PhoneCode = "+1246", ApiCode = "bb" },
                new CountryInfo { Code = "bd", Name = "孟加拉国 (Bangladesh) +880", PhoneCode = "+880", ApiCode = "bd" },
                new CountryInfo { Code = "be", Name = "比利时 (Belgium) +32", PhoneCode = "+32", ApiCode = "be" },
                new CountryInfo { Code = "bf", Name = "布基纳法索 (Burkina Faso) +226", PhoneCode = "+226", ApiCode = "bf" },
                new CountryInfo { Code = "bg", Name = "保加利亚 (Bulgaria) +359", PhoneCode = "+359", ApiCode = "bg" },
                new CountryInfo { Code = "bh", Name = "巴林 (Bahrain) +973", PhoneCode = "+973", ApiCode = "bh" },
                new CountryInfo { Code = "bi", Name = "布隆迪 (Burundi) +257", PhoneCode = "+257", ApiCode = "bi" },
                new CountryInfo { Code = "bj", Name = "贝宁 (Benin) +229", PhoneCode = "+229", ApiCode = "bj" },
                new CountryInfo { Code = "bm", Name = "百慕大群岛 (Bermuda) +1441", PhoneCode = "+1441", ApiCode = "bm" },
                new CountryInfo { Code = "bn", Name = "文莱 (Brunei Darussalam) +673", PhoneCode = "+673", ApiCode = "bn" },
                new CountryInfo { Code = "bs", Name = "巴哈马 (Bahamas) +1242", PhoneCode = "+1242", ApiCode = "bs" },
                new CountryInfo { Code = "bt", Name = "不丹 (Bhutan) +975", PhoneCode = "+975", ApiCode = "bt" },
                new CountryInfo { Code = "by", Name = "白俄罗斯 (Belarus) +375", PhoneCode = "+375", ApiCode = "by" },
                new CountryInfo { Code = "bz", Name = "伯利兹 (Belize) +501", PhoneCode = "+501", ApiCode = "bz" },
                //new CountryInfo { Code = "cd", Name = "刚果民主共和国 (Congo, the Democratic Republic of the) +243", PhoneCode = "+243", ApiCode = "cd" },
                //new CountryInfo { Code = "cf", Name = "中非共和国 (Central African Republic) +236", PhoneCode = "+236", ApiCode = "cf" },
                new CountryInfo { Code = "cg", Name = "刚果 (Congo) +242", PhoneCode = "+242", ApiCode = "cg" },
                new CountryInfo { Code = "ch", Name = "瑞士 (Switzerland) +41", PhoneCode = "+41", ApiCode = "ch" },
                //new CountryInfo { Code = "ci", Name = "科特迪瓦 (Cote d'Ivoire/Ivory Coast) +225", PhoneCode = "+225", ApiCode = "ci" },
                new CountryInfo { Code = "ck", Name = "库克群岛 (Cook Islands) +682", PhoneCode = "+682", ApiCode = "ck" },
                new CountryInfo { Code = "cm", Name = "喀麦隆 (Cameroon) +237", PhoneCode = "+237", ApiCode = "cm" },
                new CountryInfo { Code = "cr", Name = "哥斯达黎加 (Costa Rica) +506", PhoneCode = "+506", ApiCode = "cr" },
                new CountryInfo { Code = "cu", Name = "古巴 (Cuba) +53", PhoneCode = "+53", ApiCode = "cu" },
                new CountryInfo { Code = "cv", Name = "佛得角 (Cape Verde) +238", PhoneCode = "+238", ApiCode = "cv" },
                new CountryInfo { Code = "cw", Name = "库拉索 (Curaçao) +5999", PhoneCode = "+5999", ApiCode = "cw" },
                new CountryInfo { Code = "cy", Name = "塞浦路斯 (Cyprus) +357", PhoneCode = "+357", ApiCode = "cy" },
                new CountryInfo { Code = "cz", Name = "捷克共和国 (Czech Republic) +420", PhoneCode = "+420", ApiCode = "cz" },
                new CountryInfo { Code = "dj", Name = "吉布提 (Djibouti) +253", PhoneCode = "+253", ApiCode = "dj" },
                new CountryInfo { Code = "dk", Name = "丹麦 (Denmark) +45", PhoneCode = "+45", ApiCode = "dk" },
                new CountryInfo { Code = "dm", Name = "多米尼克 (Dominica) +1767", PhoneCode = "+1767", ApiCode = "dm" },
                //new CountryInfo { Code = "do", Name = "多米尼加共和国 (Dominican Republic) +1809", PhoneCode = "+1809", ApiCode = "do" },
                new CountryInfo { Code = "ee", Name = "爱沙尼亚 (Estonia) +372", PhoneCode = "+372", ApiCode = "ee" },
                new CountryInfo { Code = "er", Name = "厄立特里亚 (Eritrea) +291", PhoneCode = "+291", ApiCode = "er" },
                new CountryInfo { Code = "es", Name = "西班牙 (Spain) +34", PhoneCode = "+34", ApiCode = "es" },
                new CountryInfo { Code = "fi", Name = "芬兰 (Finland) +358", PhoneCode = "+358", ApiCode = "fi" },
                new CountryInfo { Code = "fj", Name = "斐济群岛 (Fiji) +679", PhoneCode = "+679", ApiCode = "fj" },
                //new CountryInfo { Code = "fk", Name = "福克兰群岛(马尔维纳斯群岛) (Falkland Islands (Malvinas)) +500", PhoneCode = "+500", ApiCode = "fk" },
                //new CountryInfo { Code = "fm", Name = "密克罗尼西亚 (Micronesia, Federated States of) +691", PhoneCode = "+691", ApiCode = "fm" },
                new CountryInfo { Code = "fo", Name = "法罗群岛 (Faroe Islands) +298", PhoneCode = "+298", ApiCode = "fo" },
                new CountryInfo { Code = "ga", Name = "加蓬 (Gabon) +241", PhoneCode = "+241", ApiCode = "ga" },
                new CountryInfo { Code = "gd", Name = "格林纳达 (Grenada) +1473", PhoneCode = "+1473", ApiCode = "gd" },
                new CountryInfo { Code = "ge", Name = "乔治亚 (Georgia) +995", PhoneCode = "+995", ApiCode = "ge" },
                new CountryInfo { Code = "gf", Name = "法属圭亚那 (French Guiana) +594", PhoneCode = "+594", ApiCode = "gf" },
                new CountryInfo { Code = "gh", Name = "加纳 (Ghana) +233", PhoneCode = "+233", ApiCode = "gh" },
                new CountryInfo { Code = "gi", Name = "直布罗陀 (Gibraltar) +350", PhoneCode = "+350", ApiCode = "gi" },
                new CountryInfo { Code = "gl", Name = "格陵兰 (Greenland) +299", PhoneCode = "+299", ApiCode = "gl" },
                new CountryInfo { Code = "gm", Name = "冈比亚 (Gambia) +220", PhoneCode = "+220", ApiCode = "gm" },
                new CountryInfo { Code = "gn", Name = "几内亚 (Guinea) +224", PhoneCode = "+224", ApiCode = "gn" },
                //new CountryInfo { Code = "gq", Name = "赤道几内亚 (Equatorial Guinea) +240", PhoneCode = "+240", ApiCode = "gq" },
                new CountryInfo { Code = "gr", Name = "希腊 (Greece) +30", PhoneCode = "+30", ApiCode = "gr" },
                new CountryInfo { Code = "gt", Name = "危地马拉 (Guatemala) +502", PhoneCode = "+502", ApiCode = "gt" },
                new CountryInfo { Code = "gu", Name = "关岛 (Guam) +1671", PhoneCode = "+1671", ApiCode = "gu" },
                //new CountryInfo { Code = "gw", Name = "几内亚比绍 (Guinea-Bissau) +245", PhoneCode = "+245", ApiCode = "gw" },
                new CountryInfo { Code = "gy", Name = "圭亚那 (Guyana) +592", PhoneCode = "+592", ApiCode = "gy" },
                new CountryInfo { Code = "hn", Name = "洪都拉斯 (Honduras) +504", PhoneCode = "+504", ApiCode = "hn" },
                new CountryInfo { Code = "hr", Name = "克罗地亚 (Croatia) +385", PhoneCode = "+385", ApiCode = "hr" },
                new CountryInfo { Code = "ht", Name = "海地 (Haiti) +509", PhoneCode = "+509", ApiCode = "ht" },
                new CountryInfo { Code = "hu", Name = "匈牙利 (Hungary) +36", PhoneCode = "+36", ApiCode = "hu" },
                new CountryInfo { Code = "ie", Name = "爱尔兰 (Ireland) +353", PhoneCode = "+353", ApiCode = "ie" },
                new CountryInfo { Code = "il", Name = "以色列 (Israel) +972", PhoneCode = "+972", ApiCode = "il" },
                new CountryInfo { Code = "iq", Name = "伊拉克 (Iraq) +964", PhoneCode = "+964", ApiCode = "iq" },
                new CountryInfo { Code = "ir", Name = "伊朗 (Iran, Islamic Republic of) +98", PhoneCode = "+98", ApiCode = "ir" },
                new CountryInfo { Code = "is", Name = "冰岛 (Iceland) +354", PhoneCode = "+354", ApiCode = "is" },
                new CountryInfo { Code = "it", Name = "意大利 (Italy) +39", PhoneCode = "+39", ApiCode = "it" },
                new CountryInfo { Code = "jm", Name = "牙买加 (Jamaica) +1876", PhoneCode = "+1876", ApiCode = "jm" },
                new CountryInfo { Code = "jo", Name = "约旦 (Jordan) +962", PhoneCode = "+962", ApiCode = "jo" },
                new CountryInfo { Code = "kg", Name = "吉尔吉斯斯坦 (Kyrgyzstan) +996", PhoneCode = "+996", ApiCode = "kg" },
                new CountryInfo { Code = "kh", Name = "柬埔寨 (Cambodia) +855", PhoneCode = "+855", ApiCode = "kh" },
                new CountryInfo { Code = "ki", Name = "基里巴斯 (Kiribati) +686", PhoneCode = "+686", ApiCode = "ki" },
                new CountryInfo { Code = "km", Name = "科摩罗 (Comoros) +269", PhoneCode = "+269", ApiCode = "km" },
                //new CountryInfo { Code = "kn", Name = "圣基茨和尼维斯 (Saint Kitts and Nevis) +1869", PhoneCode = "+1869", ApiCode = "kn" },
                new CountryInfo { Code = "kp", Name = "朝鲜 (Korea, Republic of) +850", PhoneCode = "+850", ApiCode = "kp" },
                new CountryInfo { Code = "kw", Name = "科威特 (Kuwait) +965", PhoneCode = "+965", ApiCode = "kw" },
                new CountryInfo { Code = "ky", Name = "开曼群岛 (Cayman Islands) +1345", PhoneCode = "+1345", ApiCode = "ky" },
                new CountryInfo { Code = "kz", Name = "哈萨克斯坦 (Kazakhstan) +7", PhoneCode = "+7", ApiCode = "kz" },
                new CountryInfo { Code = "la", Name = "老挝 (Lao People'sRepublic) +856", PhoneCode = "+856", ApiCode = "la" },
                new CountryInfo { Code = "lb", Name = "黎巴嫩 (Lebanon) +961", PhoneCode = "+961", ApiCode = "lb" },
                new CountryInfo { Code = "lc", Name = "圣卢西亚 (Saint Lucia) +1758", PhoneCode = "+1758", ApiCode = "lc" },
                new CountryInfo { Code = "li", Name = "列支敦士登 (Liechtenstein) +423", PhoneCode = "+423", ApiCode = "li" },
                new CountryInfo { Code = "lk", Name = "斯里兰卡 (Sri Lanka) +94", PhoneCode = "+94", ApiCode = "lk" },
                new CountryInfo { Code = "lr", Name = "利比里亚 (Liberia) +231", PhoneCode = "+231", ApiCode = "lr" },
                new CountryInfo { Code = "ls", Name = "莱索托 (Lesotho) +266", PhoneCode = "+266", ApiCode = "ls" },
                new CountryInfo { Code = "lt", Name = "立陶宛 (Lithuania) +370", PhoneCode = "+370", ApiCode = "lt" },
                new CountryInfo { Code = "lu", Name = "卢森堡 (Luxembourg) +352", PhoneCode = "+352", ApiCode = "lu" },
                new CountryInfo { Code = "lv", Name = "拉脱维亚 (Latvia) +371", PhoneCode = "+371", ApiCode = "lv" },
                new CountryInfo { Code = "mc", Name = "摩纳哥 (Monaco) +377", PhoneCode = "+377", ApiCode = "mc" },
                new CountryInfo { Code = "md", Name = "摩尔多瓦 (Moldova, Republic of) +373", PhoneCode = "+373", ApiCode = "md" },
                new CountryInfo { Code = "me", Name = "门的内哥罗(黑山) (Montenegro) +382", PhoneCode = "+382", ApiCode = "me" },
                new CountryInfo { Code = "mh", Name = "马绍尔群岛 (Marshall Islands) +692", PhoneCode = "+692", ApiCode = "mh" },
                //new CountryInfo { Code = "mk", Name = "马其顿,前南斯拉夫共和国 (Macedonia - The Frm Yugoslav Rep Of) +389", PhoneCode = "+389", ApiCode = "mk" },
                new CountryInfo { Code = "ml", Name = "马里 (Mali) +223", PhoneCode = "+223", ApiCode = "ml" },
                new CountryInfo { Code = "mm", Name = "缅甸 (Myanmar) +95", PhoneCode = "+95", ApiCode = "mm" },
                new CountryInfo { Code = "mn", Name = "蒙古 (Mongolia) +976", PhoneCode = "+976", ApiCode = "mn" },
                new CountryInfo { Code = "mo", Name = "中国澳门 (Macao) +853", PhoneCode = "+853", ApiCode = "mo" },
                //new CountryInfo { Code = "mp", Name = "北马里亚纳群岛 (Northern Mariana Islands) +1670", PhoneCode = "+1670", ApiCode = "mp" },
                new CountryInfo { Code = "mq", Name = "马提尼克岛 (Martinique) +596", PhoneCode = "+596", ApiCode = "mq" },
                new CountryInfo { Code = "mr", Name = "毛里塔尼亚 (Mauritania) +222", PhoneCode = "+222", ApiCode = "mr" },
                new CountryInfo { Code = "ms", Name = "蒙特塞拉特 (Montserrat) +1664", PhoneCode = "+1664", ApiCode = "ms" },
                new CountryInfo { Code = "mt", Name = "马耳他 (Malta) +356", PhoneCode = "+356", ApiCode = "mt" },
                new CountryInfo { Code = "mv", Name = "马尔代夫 (Maldives) +960", PhoneCode = "+960", ApiCode = "mv" },
                new CountryInfo { Code = "na", Name = "纳米比亚 (Namibia) +264", PhoneCode = "+264", ApiCode = "na" },
                //new CountryInfo { Code = "nc", Name = "新喀里多尼亚 (New Caledonia) +687", PhoneCode = "+687", ApiCode = "nc" },
                new CountryInfo { Code = "ne", Name = "尼日尔 (Niger) +227", PhoneCode = "+227", ApiCode = "ne" },
                new CountryInfo { Code = "ni", Name = "尼加拉瓜 (Nicaragua) +505", PhoneCode = "+505", ApiCode = "ni" },
                new CountryInfo { Code = "nl", Name = "荷兰 (Netherlands) +31", PhoneCode = "+31", ApiCode = "nl" },
                new CountryInfo { Code = "no", Name = "挪威 (Norway) +47", PhoneCode = "+47", ApiCode = "no" },
                new CountryInfo { Code = "np", Name = "尼泊尔 (Nepal) +977", PhoneCode = "+977", ApiCode = "np" },
                new CountryInfo { Code = "nr", Name = "瑙鲁 (Nauru) +674", PhoneCode = "+674", ApiCode = "nr" },
                new CountryInfo { Code = "nu", Name = "纽埃 (Niue) +683", PhoneCode = "+683", ApiCode = "nu" },
                new CountryInfo { Code = "nz", Name = "新西兰 (New Zealand) +64", PhoneCode = "+64", ApiCode = "nz" },
                new CountryInfo { Code = "om", Name = "阿曼 (Oman) +968", PhoneCode = "+968", ApiCode = "om" },
                new CountryInfo { Code = "pa", Name = "巴拿马 (Panama) +507", PhoneCode = "+507", ApiCode = "pa" },
                //new CountryInfo { Code = "pf", Name = "法属波利尼西亚 (French Polynesia) +689", PhoneCode = "+689", ApiCode = "pf" },
                //new CountryInfo { Code = "pg", Name = "巴布亚新几内亚 (Papua New Guinea) +675", PhoneCode = "+675", ApiCode = "pg" },
                new CountryInfo { Code = "pk", Name = "巴基斯坦 (Pakistan) +92", PhoneCode = "+92", ApiCode = "pk" },
                new CountryInfo { Code = "pl", Name = "波兰 (Poland) +48", PhoneCode = "+48", ApiCode = "pl" },
                //new CountryInfo { Code = "pm", Name = "圣皮埃尔岛和密克隆岛 (Saint Pierre and Miquelon) +508", PhoneCode = "+508", ApiCode = "pm" },
                new CountryInfo { Code = "pr", Name = "波多黎各 (Puerto Rico) +1787", PhoneCode = "+1787", ApiCode = "pr" },
                //new CountryInfo { Code = "ps", Name = "巴勒斯坦当局 (Palestine, State of) +970", PhoneCode = "+970", ApiCode = "ps" },
                new CountryInfo { Code = "pt", Name = "葡萄牙 (Portugal) +351", PhoneCode = "+351", ApiCode = "pt" },
                new CountryInfo { Code = "pw", Name = "帕劳群岛 (Palau) +680", PhoneCode = "+680", ApiCode = "pw" },
                new CountryInfo { Code = "qa", Name = "卡塔尔 (Qatar) +974", PhoneCode = "+974", ApiCode = "qa" },
                new CountryInfo { Code = "ro", Name = "罗马尼亚 (Romania) +40", PhoneCode = "+40", ApiCode = "ro" },
                new CountryInfo { Code = "rs", Name = "塞尔维亚 (Serbia) +381", PhoneCode = "+381", ApiCode = "rs" },
                new CountryInfo { Code = "rw", Name = "卢旺达 (Rwanda) +250", PhoneCode = "+250", ApiCode = "rw" },
                new CountryInfo { Code = "sa", Name = "沙特阿拉伯 (Saudi Arabia) +966", PhoneCode = "+966", ApiCode = "sa" },
                new CountryInfo { Code = "sb", Name = "所罗门群岛 (Solomon Islands) +677", PhoneCode = "+677", ApiCode = "sb" },
                new CountryInfo { Code = "se", Name = "瑞典 (Sweden) +46", PhoneCode = "+46", ApiCode = "se" },
                new CountryInfo { Code = "si", Name = "斯洛文尼亚 (Slovenia) +386", PhoneCode = "+386", ApiCode = "si" },
                new CountryInfo { Code = "sk", Name = "斯洛伐克 (Slovakia) +421", PhoneCode = "+421", ApiCode = "sk" },
                new CountryInfo { Code = "sl", Name = "塞拉利昂 (Sierra Leone) +232", PhoneCode = "+232", ApiCode = "sl" },
                new CountryInfo { Code = "sm", Name = "圣马力诺 (San Marino) +378", PhoneCode = "+378", ApiCode = "sm" },
                new CountryInfo { Code = "sn", Name = "塞内加尔 (Senegal) +221", PhoneCode = "+221", ApiCode = "sn" },
                new CountryInfo { Code = "so", Name = "索马里 (Somalia) +252", PhoneCode = "+252", ApiCode = "so" },
                new CountryInfo { Code = "sr", Name = "苏里南 (Suriname) +597", PhoneCode = "+597", ApiCode = "sr" },
                new CountryInfo { Code = "ss", Name = "南苏丹共和国 (South Sudan) +211", PhoneCode = "+211", ApiCode = "ss" },
                //new CountryInfo { Code = "st", Name = "圣多美和普林西比 (Sao Tome and Principe) +239", PhoneCode = "+239", ApiCode = "st" },
                new CountryInfo { Code = "sv", Name = "萨尔瓦多 (El Salvador) +503", PhoneCode = "+503", ApiCode = "sv" },
                new CountryInfo { Code = "sy", Name = "叙利亚 (Syrian Arab Republic) +963", PhoneCode = "+963", ApiCode = "sy" },
                new CountryInfo { Code = "sz", Name = "斯威士兰 (Swaziland) +268", PhoneCode = "+268", ApiCode = "sz" },
                //new CountryInfo { Code = "tc", Name = "特克斯群岛和凯科斯群岛 (Turks and Caicos Islands) +1649", PhoneCode = "+1649", ApiCode = "tc" },
                new CountryInfo { Code = "td", Name = "乍得 (Chad) +235", PhoneCode = "+235", ApiCode = "td" },
                new CountryInfo { Code = "tg", Name = "多哥 (Togo) +228", PhoneCode = "+228", ApiCode = "tg" },
                new CountryInfo { Code = "tj", Name = "塔吉克斯坦 (Tajikistan) +992", PhoneCode = "+992", ApiCode = "tj" },
                new CountryInfo { Code = "tl", Name = "东帝汶 (Timor-Leste) +670", PhoneCode = "+670", ApiCode = "tl" },
                //new CountryInfo { Code = "tm", Name = "土库曼斯坦 (Turkmenistan) +993", PhoneCode = "+993", ApiCode = "tm" },
                new CountryInfo { Code = "to", Name = "汤加 (Tonga) +676", PhoneCode = "+676", ApiCode = "to" },
                //new CountryInfo { Code = "tt", Name = "特立尼达和多巴哥 (Trinidad and Tobago) +1868", PhoneCode = "+1868", ApiCode = "tt" },
                new CountryInfo { Code = "ua", Name = "乌克兰 (Ukraine) +380", PhoneCode = "+380", ApiCode = "ua" },
                new CountryInfo { Code = "uz", Name = "乌兹别克斯坦 (Uzbekistan) +998", PhoneCode = "+998", ApiCode = "uz" },
                //new CountryInfo { Code = "va", Name = "梵蒂冈城 (Holy See (Vatican City State)) +379", PhoneCode = "+379", ApiCode = "va" },
                //new CountryInfo { Code = "vc", Name = "圣文森特和格林纳丁斯 (Saint Vincent and the Grenadines) +1784", PhoneCode = "+1784", ApiCode = "vc" },
                //new CountryInfo { Code = "vg", Name = "维尔京群岛（英属） (Virgin Islands, British) +1284", PhoneCode = "+1284", ApiCode = "vg" },
                //new CountryInfo { Code = "vi", Name = "维尔京群岛 (Virgin Islands, U.S.) +1340", PhoneCode = "+1340", ApiCode = "vi" },
                new CountryInfo { Code = "vu", Name = "瓦努阿图 (Vanuatu) +678", PhoneCode = "+678", ApiCode = "vu" },
                //new CountryInfo { Code = "wf", Name = "瓦利斯群岛和富图纳群岛 (Wallis and Futuna) +681", PhoneCode = "+681", ApiCode = "wf" },
                new CountryInfo { Code = "ws", Name = "萨摩亚 (Samoa) +685", PhoneCode = "+685", ApiCode = "ws" },
                new CountryInfo { Code = "ye", Name = "也门 (Yemen) +967", PhoneCode = "+967", ApiCode = "ye" }
            };
        }

        public static CountryInfo? GetCountryByCode(string code)
        {
            var countries = GetSupportedCountries();
            return countries.FirstOrDefault(c => c.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
        }

        public static CountryInfo? GetCountryByPhoneCode(string phoneCode)
        {
            var countries = GetSupportedCountries();
            return countries.FirstOrDefault(c => c.PhoneCode.Equals(phoneCode, StringComparison.OrdinalIgnoreCase));
        }
    }
}
