using System.ComponentModel;

namespace AWSAutoRegister.Models
{
    public class RegistrationData : INotifyPropertyChanged
    {
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string OrganizationName { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string CardNumber { get; set; } = string.Empty;
        public string ExpiryMonth { get; set; } = string.Empty;
        public string ExpiryYear { get; set; } = string.Empty;
        public string SecurityCode { get; set; } = string.Empty;
        public string CardholderName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty; // 新增手机号码字段
        public string EmailPassword { get; set; } = string.Empty; // 新增邮箱密码字段
        public string CountryCode { get; set; } = "US"; // 新增国家代码字段，默认为US
        public string MfaInfo { get; set; } = string.Empty; // 新增MFA信息字段
        public string AccessKey { get; set; } = string.Empty; // AWS访问密钥
        public string SecretAccessKey { get; set; } = string.Empty; // AWS秘密访问密钥

        // 状态管理字段
        private DataProcessStatus _status = DataProcessStatus.Unprocessed;
        public DataProcessStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(StatusIcon));
                }
            }
        }

        public DateTime? StartTime { get; set; }
        public DateTime? CompletedTime { get; set; }
        public string CurrentStep { get; set; } = "待处理";
        public string ErrorMessage { get; set; } = string.Empty;

        // 多线程相关属性
        public int AssignedThreadId { get; set; } = 0; // 分配的线程ID，0表示单线程模式
        public DataStatus ProcessingStatus { get; set; } = DataStatus.Unprocessed; // 处理状态
        public DateTime AssignedTimestamp { get; set; } = DateTime.MinValue; // 分配时间戳
        public string ProcessingNotes { get; set; } = string.Empty; // 处理备注

        // 用于数据绑定的属性
        public string StatusText => GetStatusText();
        public string StatusIcon => GetStatusIcon();

        // INotifyPropertyChanged 实现
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public static RegistrationData FromLine(string line)
        {
            var fields = line.Split('|');
            if (fields.Length < 13)
                throw new ArgumentException("数据格式不正确，需要至少13个字段");

            var data = new RegistrationData
            {
                Email = fields[0].Trim(),
                Password = fields[1].Trim(),
                FullName = fields[2].Trim(),
                OrganizationName = fields[3].Trim(),
                Address = fields[4].Trim(),
                City = fields[5].Trim(),
                State = fields[6].Trim(),
                PostalCode = fields[7].Trim(),
                CardNumber = fields[8].Trim(),
                ExpiryMonth = fields[9].Trim(),
                ExpiryYear = fields[10].Trim(),
                SecurityCode = fields[11].Trim(),
                CardholderName = fields[12].Trim()
            };

            // 检查是否有第14个字段（邮箱密码）
            if (fields.Length >= 14)
            {
                data.EmailPassword = fields[13].Trim();
            }

            // 检查是否有第15个字段（国家代码）
            if (fields.Length >= 15)
            {
                data.CountryCode = fields[14].Trim().ToUpper();
            }
            else
            {
                data.CountryCode = "US"; // 默认为美国
            }



            return data;
        }

        public string ToLine()
        {
            return $"{Email}|{Password}|{FullName}|{OrganizationName}|{Address}|{City}|{State}|{PostalCode}|{CardNumber}|{ExpiryMonth}|{ExpiryYear}|{SecurityCode}|{CardholderName}|{EmailPassword}|{CountryCode}";
        }

        public string GetMonthName()
        {
            return ExpiryMonth switch
            {
                "01" or "1" => "January",
                "02" or "2" => "February",
                "03" or "3" => "March",
                "04" or "4" => "April",
                "05" or "5" => "May",
                "06" or "6" => "June",
                "07" or "7" => "July",
                "08" or "8" => "August",
                "09" or "9" => "September",
                "10" => "October",
                "11" => "November",
                "12" => "December",
                _ => "January"
            };
        }

        public string GetFullYear()
        {
            if (ExpiryYear.Length == 2)
                return "20" + ExpiryYear;
            return ExpiryYear;
        }

        // 状态显示方法
        public string GetStatusIcon()
        {
            return Status switch
            {
                DataProcessStatus.Unprocessed => "⚪",
                DataProcessStatus.Pending => "🔵",
                DataProcessStatus.Processing => "🟡",
                DataProcessStatus.Completed => "🟢",
                DataProcessStatus.Failed => "🔴",
                DataProcessStatus.Paused => "⏸️",
                _ => "⚪"
            };
        }

        public string GetStatusText()
        {
            return Status switch
            {
                DataProcessStatus.Unprocessed => "未处理",
                DataProcessStatus.Pending => "未使用",
                DataProcessStatus.Processing => "正在注册",
                DataProcessStatus.Completed => "已被使用",
                DataProcessStatus.Failed => "已被使用",
                DataProcessStatus.Paused => "正在注册",
                _ => "未处理"
            };
        }

        public string GetTimeDisplay()
        {
            if (CompletedTime.HasValue)
                return CompletedTime.Value.ToString("HH:mm");
            else if (StartTime.HasValue)
                return StartTime.Value.ToString("HH:mm");
            else
                return "--";
        }
    }

    public enum DataProcessStatus
    {
        Unprocessed, // ⚪ 未处理 (新增状态)
        Pending,     // 🔵 待处理
        Processing,  // 🟡 处理中
        Completed,   // 🟢 已完成
        Failed,      // 🔴 失败
        Paused       // ⏸️ 暂停
    }

    public enum RegistrationStatus
    {
        Idle,
        Running,
        Paused,
        WaitingForVerification,
        WaitingForPhoneInput,
        WaitingForSMSVerification,
        WaitingForAddressCorrection,
        Completed,
        Error,
        Terminated  // 手动终止状态
    }
}
