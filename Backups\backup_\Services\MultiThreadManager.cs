using System.Collections.Concurrent;
using AWSAutoRegister.Models;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 多线程管理器 - 统一管理所有注册线程的生命周期和资源分配
    /// </summary>
    public class MultiThreadManager
    {
        private readonly object _lockObject = new object();
        private readonly Dictionary<int, RegistrationThread> _threads = new Dictionary<int, RegistrationThread>();
        private readonly Dictionary<int, Queue<RegistrationData>> _threadQueues = new Dictionary<int, Queue<RegistrationData>>();
        private readonly Dictionary<int, HashSet<string>> _threadDataIds = new Dictionary<int, HashSet<string>>();
        private readonly ConfigService _configService;
        private readonly LogService _logService;

        // 手机号码获取相关
        private static bool _phoneNumbersObtained = false;
        private static readonly object _phoneNumberLock = new object();
        private MultiThreadPhoneNumberManager? _phoneNumberManager;
        
        // 事件
        public event Action<int, ThreadStatusInfo>? ThreadStatusChanged;
        public event Action<int, string>? ThreadManualActionRequired;
        public event Action<int, RegistrationData>? ThreadDataCompleted;
        public event Action<int, RegistrationData>? ThreadDataStarted;
        public event Action<string>? OverallStatusChanged;
        public event Action<int, string>? ThreadSaveClipboardInfo; // 线程剪贴板保存事件
        public event Action<int, string, RegistrationData?>? ThreadSaveFailedData; // 线程失败数据保存事件
        public event Action<int>? ThreadPage2Completed; // 线程完成第二页事件

        public MultiThreadManager(ConfigService configService)
        {
            _configService = configService;
            _logService = LogService.Instance;
        }

        /// <summary>
        /// 启动多线程注册
        /// </summary>
        public async Task<bool> StartMultiThreadRegistration(int threadCount, List<RegistrationData> data)
        {
            try
            {
                _logService.LogInfo($"开始启动多线程注册，线程数量: {threadCount}，数据条数: {data.Count}");
                OverallStatusChanged?.Invoke($"正在启动{threadCount}个线程...");

                // 1. 验证参数
                if (threadCount < 2 || threadCount > 6)
                {
                    _logService.LogError($"线程数量无效: {threadCount}，必须在2-6之间");
                    return false;
                }

                if (data.Count == 0)
                {
                    _logService.LogError("没有可处理的数据");
                    return false;
                }

                // 验证数据数量是否足够分配给所有线程
                if (data.Count < threadCount)
                {
                    _logService.LogError($"数据数量不足：只有{data.Count}条数据，但需要启动{threadCount}个线程。每个线程至少需要1条数据。");
                    OverallStatusChanged?.Invoke($"启动失败：数据不足（{data.Count}条数据 < {threadCount}个线程）");
                    return false;
                }

                // 2. 清理现有线程
                await StopAllThreads();

                // 3. 初始化多线程服务
                await InitializeMultiThreadServices(threadCount);

                // 4. 数据安全分配
                DistributeDataSafely(data, threadCount);

                // 5. 创建并启动各线程
                for (int i = 0; i < threadCount; i++)
                {
                    int threadId = i + 1;
                    var windowPosition = CalculateWindowPosition(threadId, threadCount);
                    var fingerprint = GenerateBrowserFingerprint();

                    var thread = new RegistrationThread(threadId, _configService, windowPosition, fingerprint, _phoneNumberManager);

                    // 订阅线程事件
                    thread.StatusChanged += OnThreadStatusChanged;
                    thread.ManualActionRequired += OnThreadManualActionRequired;
                    thread.DataCompleted += OnThreadDataCompleted;
                    thread.DataStarted += OnThreadDataStarted;
                    thread.SaveClipboardInfo += OnThreadSaveClipboardInfo;
                    thread.SaveFailedData += OnThreadSaveFailedData;
                    thread.Page2Completed += OnThreadPage2Completed;

                    _threads[threadId] = thread;

                    // 为线程分配数据
                    if (_threadQueues.TryGetValue(threadId, out var queue))
                    {
                        while (queue.Count > 0)
                        {
                            thread.AddDataToQueue(queue.Dequeue());
                        }
                    }

                    // 立即触发状态更新，确保线程在界面中显示
                    var initialStatus = new ThreadStatusInfo
                    {
                        Status = ThreadStatus.NotStarted,
                        Message = "线程已创建，等待启动",
                        Progress = 0,
                        CurrentOperation = "等待启动"
                    };
                    ThreadStatusChanged?.Invoke(threadId, initialStatus);

                    _logService.LogInfo($"线程{threadId}已创建，窗口位置: ({windowPosition.X}, {windowPosition.Y})");
                }

                // 6. 启动所有线程
                var startTasks = new List<Task>();
                foreach (var thread in _threads.Values)
                {
                    startTasks.Add(Task.Run(() => thread.StartRegistrationAsync()));
                }

                _logService.LogInfo($"多线程注册启动成功，共{threadCount}个线程");
                OverallStatusChanged?.Invoke($"多线程注册已启动，共{threadCount}个线程");
                return true;
            }
            catch (Exception ex)
            {
                _logService.LogError($"启动多线程注册失败: {ex.Message}");
                OverallStatusChanged?.Invoke($"启动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 初始化多线程服务
        /// </summary>
        private async Task InitializeMultiThreadServices(int threadCount)
        {
            try
            {
                _logService.LogInfo("正在初始化多线程服务...");

                // 1. 初始化手机号码管理器（但不立即获取手机号码）
                var phoneConfig = _configService.PhoneApi;
                PhoneApiService? phoneApiService = null;
                UsaPhoneApiService? usaPhoneApiService = null;
                QianchuanPhoneApiService? qianchuanPhoneApiService = null;

                if (phoneConfig.Provider == PhoneProvider.Durian)
                {
                    if (string.IsNullOrEmpty(phoneConfig.Name) || string.IsNullOrEmpty(phoneConfig.ApiKey))
                    {
                        throw new InvalidOperationException("榴莲API配置不完整，请先配置手机API");
                    }

                    phoneApiService = new PhoneApiService(
                        phoneConfig.BaseUrl,
                        phoneConfig.Name,
                        phoneConfig.ApiKey,
                        phoneConfig.ProjectId,
                        phoneConfig.CountryCode
                    );

                    _logService.LogInfo("榴莲手机API服务已初始化");
                }
                else if (phoneConfig.Provider == PhoneProvider.Qianchuan)
                {
                    if (string.IsNullOrEmpty(phoneConfig.QianchuanToken))
                    {
                        throw new InvalidOperationException("千川API配置不完整，请检查QianchuanToken");
                    }

                    qianchuanPhoneApiService = new QianchuanPhoneApiService(
                        phoneConfig.QianchuanBaseUrl,
                        phoneConfig.QianchuanToken,
                        phoneConfig.QianchuanChannelId
                    );

                    _logService.LogInfo("千川手机API服务已初始化");
                }
                else if (phoneConfig.Provider == PhoneProvider.USA)
                {
                    if (string.IsNullOrEmpty(phoneConfig.UsaApiToken))
                    {
                        throw new InvalidOperationException("美国API配置不完整，请检查UsaApiToken");
                    }

                    usaPhoneApiService = new UsaPhoneApiService(
                        phoneConfig.UsaBaseUrl,
                        phoneConfig.UsaApiToken
                    );

                    _logService.LogInfo("美国手机API服务已初始化");
                }
                else if (phoneConfig.Provider == PhoneProvider.Manual)
                {
                    throw new InvalidOperationException("多线程模式不支持手动手机号码模式");
                }
                else
                {
                    throw new InvalidOperationException($"不支持的手机服务商类型: {phoneConfig.Provider}");
                }

                _phoneNumberManager = new MultiThreadPhoneNumberManager(phoneApiService, usaPhoneApiService, qianchuanPhoneApiService, phoneConfig.Provider);
                _logService.LogInfo($"手机号码管理器已初始化，服务商: {phoneConfig.Provider}，将在第一个线程完成第二页后获取手机号码");

                // 2. 设置验证码管理器的服务
                if (phoneApiService != null)
                {
                    ServiceLocator.SetPhoneApiService(phoneApiService);
                    PhoneNumberReleaseManager.SetPhoneApiService(phoneApiService);
                }
                else if (qianchuanPhoneApiService != null)
                {
                    ServiceLocator.SetQianchuanPhoneApiService(qianchuanPhoneApiService);
                    QianchuanPhoneNumberBlacklistManager.SetQianchuanPhoneApiService(qianchuanPhoneApiService);
                }

                // 3. 初始化多线程邮箱验证码服务
                var multiThreadEmailService = new MultiThreadEmailVerificationService();
                if (!multiThreadEmailService.IsServiceAvailable())
                {
                    _logService.LogWarning("多线程邮箱验证码服务不可用");
                }

                _logService.LogInfo("多线程服务初始化完成");
            }
            catch (Exception ex)
            {
                _logService.LogError($"初始化多线程服务失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 暂停指定线程
        /// </summary>
        public async Task<bool> PauseThread(int threadId)
        {
            try
            {
                if (_threads.TryGetValue(threadId, out var thread))
                {
                    await thread.PauseAsync();
                    _logService.LogInfo($"线程{threadId}已暂停");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logService.LogError($"暂停线程{threadId}失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 继续指定线程
        /// </summary>
        public async Task<bool> ResumeThread(int threadId)
        {
            try
            {
                if (_threads.TryGetValue(threadId, out var thread))
                {
                    await thread.ResumeAsync();
                    _logService.LogInfo($"线程{threadId}已继续");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logService.LogError($"继续线程{threadId}失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 终止指定线程
        /// </summary>
        public async Task<bool> TerminateThread(int threadId)
        {
            try
            {
                if (_threads.TryGetValue(threadId, out var thread))
                {
                    await thread.TerminateAsync();
                    
                    // 取消事件订阅
                    thread.StatusChanged -= OnThreadStatusChanged;
                    thread.ManualActionRequired -= OnThreadManualActionRequired;
                    thread.DataCompleted -= OnThreadDataCompleted;
                    thread.DataStarted -= OnThreadDataStarted;
                    thread.SaveClipboardInfo -= OnThreadSaveClipboardInfo;
                    
                    _threads.Remove(threadId);
                    _logService.LogInfo($"线程{threadId}已终止");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logService.LogError($"终止线程{threadId}失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取线程状态
        /// </summary>
        public ThreadStatusInfo? GetThreadStatus(int threadId)
        {
            if (_threads.TryGetValue(threadId, out var thread))
            {
                return thread.GetStatus();
            }
            return null;
        }

        /// <summary>
        /// 获取所有线程状态
        /// </summary>
        public Dictionary<int, ThreadStatusInfo> GetAllThreadStatus()
        {
            var result = new Dictionary<int, ThreadStatusInfo>();
            foreach (var kvp in _threads)
            {
                result[kvp.Key] = kvp.Value.GetStatus();
            }
            return result;
        }

        /// <summary>
        /// 停止所有线程
        /// </summary>
        public async Task StopAllThreads()
        {
            try
            {
                var stopTasks = new List<Task>();
                foreach (var thread in _threads.Values)
                {
                    stopTasks.Add(thread.TerminateAsync());
                }

                if (stopTasks.Count > 0)
                {
                    await Task.WhenAll(stopTasks);
                }

                // 清理所有线程
                foreach (var thread in _threads.Values)
                {
                    thread.StatusChanged -= OnThreadStatusChanged;
                    thread.ManualActionRequired -= OnThreadManualActionRequired;
                    thread.DataCompleted -= OnThreadDataCompleted;
                    thread.DataStarted -= OnThreadDataStarted;
                    thread.SaveClipboardInfo -= OnThreadSaveClipboardInfo;
                }

                _threads.Clear();
                _threadQueues.Clear();
                _threadDataIds.Clear();

                _logService.LogInfo("所有线程已停止并清理");
                OverallStatusChanged?.Invoke("所有线程已停止");
            }
            catch (Exception ex)
            {
                _logService.LogError($"停止所有线程失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 数据安全分配
        /// </summary>
        private void DistributeDataSafely(List<RegistrationData> allData, int threadCount)
        {
            lock (_lockObject)
            {
                // 清空现有队列
                _threadQueues.Clear();
                _threadDataIds.Clear();

                // 初始化队列
                for (int i = 1; i <= threadCount; i++)
                {
                    _threadQueues[i] = new Queue<RegistrationData>();
                    _threadDataIds[i] = new HashSet<string>();
                }

                // 轮询分配数据
                for (int i = 0; i < allData.Count; i++)
                {
                    int threadId = (i % threadCount) + 1;
                    var data = allData[i];

                    // 标记数据所属线程
                    data.AssignedThreadId = threadId;
                    data.ProcessingStatus = DataStatus.Assigned;
                    data.AssignedTimestamp = DateTime.Now;

                    // 同步更新UI显示状态
                    data.Status = DataProcessStatus.Pending; // 设置为待处理状态
                    data.CurrentStep = $"已分配给线程{threadId}";

                    // 加入对应线程队列
                    _threadQueues[threadId].Enqueue(data);
                    _threadDataIds[threadId].Add(data.Email); // 使用邮箱作为唯一标识

                    // 移除详细的单条数据分配日志，只在最后输出统计信息
                    // _logService.LogInfo($"数据分配: {data.Email} → 线程{threadId}");
                }

                // 注意：不清空原始列表，保持与单线程模式一致的行为
                // 数据应该保留在原位置，直到处理完成
                // allData.Clear(); // 注释掉这行，保持数据在原位置

                // 记录简要的分配总结
                _logService.LogInfo($"数据分配完成：共{allData.Count}条数据分配给{threadCount}个线程");

                // 记录各线程分配统计
                foreach (var kvp in _threadQueues)
                {
                    _logService.LogInfo($"线程{kvp.Key}分配到{kvp.Value.Count}条数据");
                }
            }
        }

        /// <summary>
        /// 智能窗口位置计算 - 兼容DPI缩放的百分比布局
        /// 布局规则：
        /// - 兼容Windows DPI缩放（125%、150%、200%等）
        /// - 使用屏幕百分比确保所有窗口完全可见
        /// - 1-3个线程：单列布局，每个窗口占屏幕宽度30%
        /// - 4-6个线程：双列布局，每列占屏幕宽度25%
        /// - 窗口高度根据线程数动态分配，确保不溢出
        /// - 增加底部安全边距，避免溢出到任务栏
        /// </summary>
        private WindowPosition CalculateWindowPosition(int threadId, int totalThreads)
        {
            // 获取屏幕工作区域大小（已考虑DPI缩放）
            var screenWidth = System.Windows.SystemParameters.WorkArea.Width;
            var screenHeight = System.Windows.SystemParameters.WorkArea.Height;

            _logService.LogInfo($"屏幕工作区域: {screenWidth}x{screenHeight}");

            // 布局参数 - 使用百分比确保兼容性
            const double gapRatio = 0.03;  // 间隙比例3%（减少间隙避免溢出）
            const double singleColumnWidthRatio = 0.30;  // 单列时每个窗口占屏幕30%宽度
            const double doubleColumnWidthRatio = 0.25;  // 双列时每个窗口占屏幕25%宽度
            const double columnGapRatio = 0.05;          // 列间隙占屏幕5%宽度
            const double bottomSafetyMarginRatio = 0.05; // 底部安全边距5%，避免溢出到任务栏

            // 确定布局：最多6个窗口，第一列最多3个
            int maxWindows = Math.Min(totalThreads, 6);
            int firstColumnWindows = Math.Min(3, maxWindows);
            int secondColumnWindows = Math.Max(0, maxWindows - 3);

            // 判断是否需要双列布局
            bool needTwoColumns = totalThreads > 3;

            // 计算当前线程在哪一列和哪一行
            int col, row, windowsInCurrentColumn;
            if (threadId <= firstColumnWindows)
            {
                // 在第一列：线程1,2,3
                col = 0;
                row = threadId - 1;
                windowsInCurrentColumn = firstColumnWindows;
            }
            else
            {
                // 在第二列：线程4,5,6
                col = 1;
                row = threadId - firstColumnWindows - 1;
                windowsInCurrentColumn = secondColumnWindows;
            }

            // 计算窗口宽度 - 使用百分比确保一致性
            double widthRatio = needTwoColumns ? doubleColumnWidthRatio : singleColumnWidthRatio;
            int windowWidth = (int)(screenWidth * widthRatio);

            // 设置合理的宽度范围（考虑小屏幕设备）
            windowWidth = Math.Max(Math.Min(windowWidth, 500), 280);

            // 计算可用高度 - 减去底部安全边距
            int bottomSafetyMargin = (int)(screenHeight * bottomSafetyMarginRatio);
            int usableHeight = (int)screenHeight - bottomSafetyMargin;

            // 计算窗口高度 - 确保所有窗口都能完全显示
            int gapHeight = (int)(usableHeight * gapRatio);
            int availableHeight = usableHeight - (windowsInCurrentColumn - 1) * gapHeight;
            int windowHeight = availableHeight / windowsInCurrentColumn;

            // 设置合理的高度范围
            windowHeight = Math.Max(Math.Min(windowHeight, 600), 200);

            // 重新验证总高度，确保不溢出（包括安全边距）
            int totalRequiredHeight = windowsInCurrentColumn * windowHeight + (windowsInCurrentColumn - 1) * gapHeight;
            if (totalRequiredHeight > usableHeight)
            {
                // 如果总高度超出可用高度，重新计算窗口高度
                windowHeight = (usableHeight - (windowsInCurrentColumn - 1) * gapHeight) / windowsInCurrentColumn;
                windowHeight = Math.Max(windowHeight, 180); // 最小高度180
            }

            // 计算窗口位置
            int x, y;

            // X坐标：根据列计算
            if (col == 0)
            {
                // 第一列：从屏幕最左边开始
                x = 0;
            }
            else
            {
                // 第二列：第一列宽度 + 间隙
                int columnGap = (int)(screenWidth * columnGapRatio);
                x = windowWidth + columnGap;
            }

            // Y坐标：根据行位置和间隙计算
            y = row * (windowHeight + gapHeight);

            // 最终边界检查，确保窗口完全在屏幕内（包括安全边距）
            x = Math.Max(0, Math.Min(x, (int)screenWidth - windowWidth));
            y = Math.Max(0, Math.Min(y, usableHeight - windowHeight));

            _logService.LogInfo($"线程{threadId}窗口布局: 位置({x}, {y}), 大小({windowWidth}x{windowHeight}), 列{col + 1}行{row + 1}, 宽度{widthRatio:P0}, 当前列窗口数:{windowsInCurrentColumn}, 间隙{gapHeight}px, 双列模式:{needTwoColumns}, 底部安全边距:{bottomSafetyMargin}px, 可用高度:{usableHeight}px");

            return new WindowPosition
            {
                X = x,
                Y = y,
                Width = windowWidth,
                Height = windowHeight
            };
        }

        /// <summary>
        /// 生成浏览器指纹
        /// </summary>
        private BrowserFingerprint GenerateBrowserFingerprint()
        {
            var userAgents = new[]
            {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36"
            };

            var timeZones = new[]
            {
                "Asia/Shanghai",
                "Asia/Tokyo",
                "Asia/Seoul",
                "Europe/London",
                "America/New_York"
            };

            var random = new Random();
            return new BrowserFingerprint
            {
                UserAgent = userAgents[random.Next(userAgents.Length)],
                TimeZone = timeZones[random.Next(timeZones.Length)],
                Language = "zh-CN,zh;q=0.9,en;q=0.8"
            };
        }

        // 事件处理方法
        private void OnThreadStatusChanged(int threadId, ThreadStatusInfo status)
        {
            ThreadStatusChanged?.Invoke(threadId, status);
        }

        private void OnThreadManualActionRequired(int threadId, string message)
        {
            ThreadManualActionRequired?.Invoke(threadId, message);
        }

        private void OnThreadDataCompleted(int threadId, RegistrationData data)
        {
            ThreadDataCompleted?.Invoke(threadId, data);
        }

        private void OnThreadDataStarted(int threadId, RegistrationData data)
        {
            ThreadDataStarted?.Invoke(threadId, data);
        }

        private void OnThreadSaveClipboardInfo(int threadId, string clipboardContent)
        {
            _logService.LogInfo($"线程{threadId}请求保存剪贴板信息: {clipboardContent}");
            ThreadSaveClipboardInfo?.Invoke(threadId, clipboardContent);
        }

        private void OnThreadSaveFailedData(int threadId, string failureReason, RegistrationData? failedData)
        {
            _logService.LogInfo($"线程{threadId}请求保存失败数据: {failureReason}, 数据: {failedData?.Email}");
            ThreadSaveFailedData?.Invoke(threadId, failureReason, failedData);
        }

        private async void OnThreadPage2Completed(int threadId)
        {
            ThreadPage2Completed?.Invoke(threadId);

            // 检查是否需要获取手机号码
            lock (_phoneNumberLock)
            {
                if (!_phoneNumbersObtained && _phoneNumberManager != null)
                {
                    _phoneNumbersObtained = true;
                    _logService.LogInfo($"线程{threadId}完成第二页，开始批量获取手机号码...");

                    // 在后台异步获取手机号码
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            var threadCount = _threads.Count;
                            var phoneSuccess = await _phoneNumberManager.BatchGetPhoneNumbersAsync(threadCount);
                            if (phoneSuccess)
                            {
                                _logService.LogInfo($"批量获取{threadCount}个手机号码成功");
                            }
                            else
                            {
                                _logService.LogWarning("批量获取手机号码失败，各线程将使用单独获取模式");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logService.LogError($"批量获取手机号码异常: {ex.Message}");
                        }
                    });
                }
                else if (_phoneNumbersObtained)
                {
                    _logService.LogInfo($"线程{threadId}完成第二页，手机号码已获取，无需重复获取");
                }
            }
        }

        /// <summary>
        /// 获取指定线程当前处理的邮箱
        /// </summary>
        public string? GetCurrentEmailForThread(int threadId)
        {
            try
            {
                if (_threads.TryGetValue(threadId, out var thread))
                {
                    var currentEmail = thread.GetCurrentEmail();
                    _logService.LogInfo($"获取线程{threadId}当前邮箱: {currentEmail ?? "无"}");
                    return currentEmail;
                }
                else
                {
                    _logService.LogWarning($"线程{threadId}不存在");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"获取线程{threadId}当前邮箱失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取指定线程当前处理的完整数据
        /// </summary>
        public RegistrationData? GetCurrentDataForThread(int threadId)
        {
            try
            {
                if (_threads.TryGetValue(threadId, out var thread))
                {
                    var currentData = thread.GetCurrentData();
                    _logService.LogInfo($"获取线程{threadId}当前数据: {currentData?.Email ?? "无"}");
                    return currentData;
                }
                else
                {
                    _logService.LogWarning($"线程{threadId}不存在");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"获取线程{threadId}当前数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取指定线程ID的注册线程
        /// </summary>
        public RegistrationThread? GetRegistrationThread(int threadId)
        {
            lock (_lockObject)
            {
                return _threads.TryGetValue(threadId, out var thread) ? thread : null;
            }
        }
    }
}
