@echo off
chcp 65001
echo 正在创建桌面快捷方式...

:: 获取当前目录路径(包含结尾的反斜杠)
set "CURRENT_DIR=%~dp0"
:: 获取桌面路径
set "DESKTOP=%USERPROFILE%\Desktop"

:: 创建菜谱快捷方式
echo 创建菜谱快捷方式...
set "TARGET=%CURRENT_DIR%菜谱.html"
set "SHORTCUT=%DESKTOP%\菜谱.lnk"
powershell -Command "$ws = New-Object -ComObject WScript.Shell; $s = $ws.CreateShortcut('%SHORTCUT%'); $s.TargetPath = '%TARGET%'; $s.WorkingDirectory = '%CURRENT_DIR%'; $s.Save()"

:: 创建AWS快捷方式
echo 创建AWS快捷方式...
set "TARGET=%CURRENT_DIR%AWS.exe"
set "SHORTCUT=%DESKTOP%\AWS.lnk"
powershell -Command "$ws = New-Object -ComObject WScript.Shell; $s = $ws.CreateShortcut('%SHORTCUT%'); $s.TargetPath = '%TARGET%'; $s.WorkingDirectory = '%CURRENT_DIR%'; $s.Save()"

:: 创建AWS验证码快捷方式
echo 创建AWS验证码快捷方式...
set "TARGET=%CURRENT_DIR%AWS验证码.exe"
set "SHORTCUT=%DESKTOP%\AWS验证码.lnk"
powershell -Command "$ws = New-Object -ComObject WScript.Shell; $s = $ws.CreateShortcut('%SHORTCUT%'); $s.TargetPath = '%TARGET%'; $s.WorkingDirectory = '%CURRENT_DIR%'; $s.Save()"

:: 创建AWS邮件查封快捷方式
echo 创建AWS邮件查封快捷方式...
set "TARGET=%CURRENT_DIR%AWS邮件查封.exe"
set "SHORTCUT=%DESKTOP%\AWS邮件查封.lnk"
powershell -Command "$ws = New-Object -ComObject WScript.Shell; $s = $ws.CreateShortcut('%SHORTCUT%'); $s.TargetPath = '%TARGET%'; $s.WorkingDirectory = '%CURRENT_DIR%'; $s.Save()"

echo 快捷方式创建完成！
pause 