手机号码获取配置也需要和图形验证码那些一样做多个自动模式的，当前手机号码自动模式配置保持不变，只不过把自动模式改成“榴莲”然后保持当前的执行代码和配置等软件交互操作
增加多一个叫做“美国”，这个也是自动手机号码模式，只不过他的接口不一样，当前自动模式的代码就给选择榴莲的使用，手动模式模式的也一样保持手动模式的代码即可
排序为：榴莲、美国、手动模式
选择美国后不需要像榴莲一样有输入框和按钮这些的，因为都是内置默认的东西，在第四页和第六页都一样是使用默认选择的+1区号，因为这个接口就只有这个国家地区

注意1：现在开始，自动手机号码模式的话就需要判断一下是哪个服务商，是美国还是榴莲的，再进行对应请求接口
注意2：自动手机模式下原本的设计是第一次获取手机验证码超时会释放号码返回上一页也就是第六页去获取新的号码，再来重新获取验证码，这个功能不放在全局，这个只有提供给选择榴莲这个接口使用的，选择美国的就按照下面的流程，他是没有重试号码的，超时的时候直接转手动模式即可。

美国的接口如下：
your_api_token_here值 = A28AFF657DC03E5B4E165400E3F1 【这个放在config.json里】 
接口地址：https://www.api21k.com
所有 API 请求都需要在请求头中包含以下信息：
Authorization: your_api_token_here
Content-Type: application/json

购买号码接口地址： 
POST /api/v1/buy/create
参数  类型  必填  默认值 描述
app_id  int 是 - 项目 ID
type  int 否 1 项目类型：1=首次卡，2=重启卡，3=续费卡
num int 是 - 购买数量
expiry  int 否 0 有效期类型：0=随机，1=5-30 天，2=10-30 天，3=15-30 天，4=30-60 天，5=60-80 天，6=大于 80 天
需要传：
app_id=81
type=1
num=1
expiry=0

获取号码响应示例：
{
  "code": 1,
  "msg": "购买成功",
  "data": {
    "ordernum": "ORD20240101123456",
    "api_count": 5
  }
}

需要把ordernum的值保存到对应的线程上，在对应线程用一个临时变量去保存ordernum的值


获取手机号码接口地址： POST /api/v1/order/api
参数  类型  必填  描述
ordernum  string  是 订单号
需要把当前线程ordernum值填写进去请求

响应示例：
{
  "code": 1,
  "msg": "获取订单API列表成功",
  "data": {
    "url_list": ["https://api.example.com"],
    "list": [
      {
        "app_id": 1,
        "cate_id": 2,
        "type": 1,
        "tel": "13800138000",
        "token": "api_token",
        "end_time": "2024-01-31 23:59:59",
        "sms_count": 0,
        "voice_count": 0,
        "remark": "",
        "status": 0,
        "api": "https://api.example.com/api/record?token=api_token"
      }
    ],
    "total": 1
  }
}

取出tel的值，这个值就是在第四页和第六页输入号码的时候填入的，填入之前去掉前面的1
再继续在对应线程用一个临时变量去保存api的值

在第六页完成验证码后，开始自动获取手机验证码的时候去请求：
对应线程当前保存的api值的地址，并且在后面加上"&format=txt1"这个参数去请求
返回的结果是：
Your Amazon Web Services (AWS) verification code is: 9199
你只需要提取里面四位纯数字即为验证码，那么直接填入并继续下一步和后面的操作
超时为2分钟，2分钟都没有获取到验证码自动转为手动模式，这个接口不做重试操作

如果正常获取到验证码后进行下一步操作后，把当前线程刚才创建的临时变量的值清空掉，保证下一个注册的时候不会用到同样的ordernum和api的值