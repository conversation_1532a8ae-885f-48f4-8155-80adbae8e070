using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using Microsoft.Win32;
using AWSAutoRegister.Models;
using AWSAutoRegister.Services;
using System.Net.Http;
using System.Threading.Tasks;
using System.Linq;

namespace AWSAutoRegister
{
    public partial class MainWindow : Window
    {
        private readonly ObservableCollection<RegistrationData> _registrationDataList = new();
        private readonly FileService _fileService = new();
        private readonly AdsService _adsService = new();
        private readonly AutomationService _automationService = new();
        private readonly ConfigService _configService = new();
        private readonly LogService _logService = LogService.Instance;
        private string _selectedFilePath = string.Empty;
        private int _currentDataIndex = 0;

        // 多线程相关字段
        private MultiThreadManager? _multiThreadManager;
        private MultiThreadWindow? _multiThreadWindow;
        private int _selectedThreadCount = 1; // 默认单线程模式

        // 进度条相关字段
        private DateTime _processingStartTime;
        private int _totalDataCount = 0;
        private int _processedCount = 0;
        private readonly System.Windows.Threading.DispatcherTimer _progressTimer = new();

        public MainWindow()
        {
            try
            {
                InitializeComponent();

                // 记录程序启动
                _logService.LogInfo("AWS自动注册工具启动");
                _logService.LogInfo($"程序版本: {System.Reflection.Assembly.GetExecutingAssembly().GetName().Version}");
                _logService.LogInfo($"启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                // 设置数据源
                DataGrid.ItemsSource = _registrationDataList;

                // 订阅事件
                _automationService.StatusChanged += OnStatusChanged;
                _automationService.RegistrationStatusChanged += OnRegistrationStatusChanged;
                _automationService.ShowInputDialog += OnShowInputDialog;
                _automationService.SaveFailedData += OnSaveFailedData;
                _automationService.DataCompleted += OnDataCompleted; // 添加缺失的DataCompleted事件订阅
                _automationService.DataCompletedWithoutKeys += OnDataCompletedWithoutKeys;
                _automationService.DataCompletedWithBillingIssue += OnDataCompletedWithBillingIssue;
                _automationService.DataCompletedWithIneligibleIssue += OnDataCompletedWithIneligibleIssue;
                _automationService.RemoveCompletedData += OnRemoveCompletedData;
                _automationService.SaveClipboardInfoToSuccessData += OnSaveClipboardInfoToSuccessData;
                // 移除DataCompletedToStep4事件订阅，因为我们只在完全注册成功时才移动数据
                // _automationService.DataCompletedToStep4 += OnDataCompletedToStep4;

                // 设置默认实例ID
                ProfileIdTextBox.Text = "k104lc10";

                // 初始化浏览器模式
                InitializeBrowserMode();

                // 初始化PhoneApi配置
                InitializePhoneApiConfig();

                // 初始化邮箱验证码配置
                InitializeEmailVerificationConfig();

                // 初始化图形验证码配置
                InitializeCaptchaRecognitionConfig();

                // 初始化线程数量选择
                InitializeThreadCountSelection();

                // 检查邮箱验证码软件状态
                CheckEmailVerificationSoftware();

                // 初始化状态统计
                UpdateDataStatistics();

                // 初始化进度条
                InitializeProgressBars();

                // 设置状态
                try { if (StatusTextBlock != null) StatusTextBlock.Text = "程序已就绪 - 双模式浏览器支持已启用"; } catch { }

                // 记录初始化完成
                _logService.LogInfo("程序初始化完成");
            }
            catch (Exception ex)
            {
                _logService.LogError($"程序初始化失败: {ex.Message}");
                MessageBox.Show($"程序初始化失败: {ex.Message}\n\n详细信息: {ex.StackTrace}", "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown();
            }
        }

        private void InitializeBrowserMode()
        {
            try
            {
                // 根据配置设置界面状态
                if (_configService.BrowserMode == Services.BrowserMode.AdsPower)
                {
                    if (AdsPowerModeRadio != null) AdsPowerModeRadio.IsChecked = true;
                    if (AdsPowerConfigGrid != null) AdsPowerConfigGrid.Visibility = Visibility.Visible;
                    if (LocalChromeConfigGrid != null) LocalChromeConfigGrid.Visibility = Visibility.Collapsed;

                    // 设置模式指示器
                    if (BrowserModeText != null) BrowserModeText.Text = "AdsPower";
                    if (BrowserModeIndicator != null) BrowserModeIndicator.Background = Brushes.Blue;
                }
                else if (_configService.BrowserMode == Services.BrowserMode.LocalChromeIncognito)
                {
                    if (LocalChromeIncognitoModeRadio != null) LocalChromeIncognitoModeRadio.IsChecked = true;
                    if (AdsPowerConfigGrid != null) AdsPowerConfigGrid.Visibility = Visibility.Collapsed;
                    if (LocalChromeConfigGrid != null) LocalChromeConfigGrid.Visibility = Visibility.Visible;

                    // 设置模式指示器
                    if (BrowserModeText != null) BrowserModeText.Text = "无痕Chrome";
                    if (BrowserModeIndicator != null) BrowserModeIndicator.Background = Brushes.Green;
                }
                else // LocalChromeNormal
                {
                    if (LocalChromeNormalModeRadio != null) LocalChromeNormalModeRadio.IsChecked = true;
                    if (AdsPowerConfigGrid != null) AdsPowerConfigGrid.Visibility = Visibility.Collapsed;
                    if (LocalChromeConfigGrid != null) LocalChromeConfigGrid.Visibility = Visibility.Visible;

                    // 设置模式指示器
                    if (BrowserModeText != null) BrowserModeText.Text = "默认Chrome";
                    if (BrowserModeIndicator != null) BrowserModeIndicator.Background = Brushes.Orange;
                }
            }
            catch (Exception ex)
            {
                // 如果初始化失败，记录错误但不阻止程序启动
                System.Diagnostics.Debug.WriteLine($"InitializeBrowserMode failed: {ex.Message}");
            }
        }

        private void BrowserModeRadio_Checked(object sender, RoutedEventArgs e)
        {
            // 确保所有控件都已初始化
            if (AdsPowerConfigGrid == null || LocalChromeConfigGrid == null || StatusTextBlock == null || _configService == null)
                return;

            // 确保发送者不为空
            if (sender == null)
                return;

            try
            {
                if (sender == AdsPowerModeRadio && AdsPowerModeRadio?.IsChecked == true)
                {
                    AdsPowerConfigGrid.Visibility = Visibility.Visible;
                    LocalChromeConfigGrid.Visibility = Visibility.Collapsed;
                    _configService.UpdateBrowserMode(Services.BrowserMode.AdsPower);
                    StatusTextBlock.Text = "已切换到 AdsPower 模式";

                    // 记录模式切换
                    _logService.LogInfo("浏览器模式切换: AdsPower 模式");

                    // 更新模式指示器
                    if (BrowserModeText != null) BrowserModeText.Text = "AdsPower";
                    if (BrowserModeIndicator != null) BrowserModeIndicator.Background = Brushes.Blue;
                }
                else if (sender == LocalChromeNormalModeRadio && LocalChromeNormalModeRadio?.IsChecked == true)
                {
                    AdsPowerConfigGrid.Visibility = Visibility.Collapsed;
                    LocalChromeConfigGrid.Visibility = Visibility.Visible;
                    _configService.UpdateBrowserMode(Services.BrowserMode.LocalChromeNormal);
                    StatusTextBlock.Text = "已切换到默认 Chrome 模式";

                    // 记录模式切换
                    _logService.LogInfo("浏览器模式切换: 默认 Chrome 模式");

                    // 更新模式指示器
                    if (BrowserModeText != null) BrowserModeText.Text = "默认Chrome";
                    if (BrowserModeIndicator != null) BrowserModeIndicator.Background = Brushes.Orange;
                }
                else if (sender == LocalChromeIncognitoModeRadio && LocalChromeIncognitoModeRadio?.IsChecked == true)
                {
                    AdsPowerConfigGrid.Visibility = Visibility.Collapsed;
                    LocalChromeConfigGrid.Visibility = Visibility.Visible;
                    _configService.UpdateBrowserMode(Services.BrowserMode.LocalChromeIncognito);
                    StatusTextBlock.Text = "已切换到无痕 Chrome 模式";

                    // 记录模式切换
                    _logService.LogInfo("浏览器模式切换: 无痕 Chrome 模式");

                    // 更新模式指示器
                    if (BrowserModeText != null) BrowserModeText.Text = "无痕Chrome";
                    if (BrowserModeIndicator != null) BrowserModeIndicator.Background = Brushes.Green;
                }

                // 检查多线程模式兼容性
                ValidateBrowserModeCompatibility();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"BrowserModeRadio_Checked error: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化线程数量选择
        /// </summary>
        private void InitializeThreadCountSelection()
        {
            try
            {
                if (ThreadCountComboBox != null)
                {
                    ThreadCountComboBox.SelectedIndex = 0; // 默认选择单线程模式
                    _selectedThreadCount = 1;
                }

                // 初始化多线程管理器
                _multiThreadManager = new MultiThreadManager(_configService);

                // 设置DialogManager的多线程管理器
                DialogManager.SetMultiThreadManager(_multiThreadManager);

                UpdateThreadModeUI();
                _logService.LogInfo("线程数量选择初始化完成");
            }
            catch (Exception ex)
            {
                _logService.LogError($"初始化线程数量选择失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 线程数量选择变化事件
        /// </summary>
        private void ThreadCountComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (ThreadCountComboBox?.SelectedItem is ComboBoxItem selectedItem &&
                    selectedItem.Tag is string tagValue &&
                    int.TryParse(tagValue, out int threadCount))
                {
                    _selectedThreadCount = threadCount;
                    _logService.LogInfo($"线程数量已选择: {threadCount}");

                    UpdateThreadModeUI();
                    ValidateBrowserModeCompatibility();
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"线程数量选择变化处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新线程模式UI
        /// </summary>
        private void UpdateThreadModeUI()
        {
            try
            {
                bool isMultiThread = _selectedThreadCount > 1;

                // 显示/隐藏多线程提示
                if (ThreadModeHint != null)
                {
                    ThreadModeHint.Visibility = isMultiThread ? Visibility.Visible : Visibility.Collapsed;
                }

                // 显示/隐藏多线程管理按钮
                if (OpenMultiThreadWindowBtn != null)
                {
                    OpenMultiThreadWindowBtn.Visibility = isMultiThread ? Visibility.Visible : Visibility.Collapsed;
                }

                // 更新状态文本
                if (StatusTextBlock != null)
                {
                    var modeText = isMultiThread ? $"多线程模式({_selectedThreadCount}线程)" : "单线程模式";
                    StatusTextBlock.Text = $"程序已就绪 - {modeText}";
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"更新线程模式UI失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证浏览器模式兼容性
        /// </summary>
        private void ValidateBrowserModeCompatibility()
        {
            try
            {
                bool isMultiThread = _selectedThreadCount > 1;
                bool isAdsPowerMode = AdsPowerModeRadio?.IsChecked == true;

                if (isMultiThread && isAdsPowerMode)
                {
                    // 多线程模式不支持AdsPower，自动切换到默认Chrome
                    if (LocalChromeNormalModeRadio != null)
                    {
                        LocalChromeNormalModeRadio.IsChecked = true;
                        _logService.LogInfo("多线程模式不支持AdsPower，已自动切换到默认Chrome模式");

                        if (StatusTextBlock != null)
                        {
                            StatusTextBlock.Text = "已自动切换到默认Chrome模式（多线程模式要求）";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"验证浏览器模式兼容性失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 打开多线程管理窗口
        /// </summary>
        private void OpenMultiThreadWindowBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_multiThreadManager == null)
                {
                    MessageBox.Show("多线程管理器未初始化", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (_multiThreadWindow == null || !_multiThreadWindow.IsLoaded)
                {
                    _multiThreadWindow = new MultiThreadWindow(_multiThreadManager, this);
                }

                _multiThreadWindow.Show();
                _multiThreadWindow.WindowState = WindowState.Normal;
                _multiThreadWindow.Activate();

                // 最小化主窗口而不是隐藏
                this.WindowState = WindowState.Minimized;

                _logService.LogInfo("多线程管理窗口已打开");
            }
            catch (Exception ex)
            {
                _logService.LogError($"打开多线程管理窗口失败: {ex.Message}");
                MessageBox.Show($"打开多线程管理窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 清理多线程窗口引用（由多线程窗口关闭时调用）
        /// </summary>
        public void ClearMultiThreadWindowReference()
        {
            _multiThreadWindow = null;
            _logService.LogInfo("多线程窗口引用已清理");
        }

        /// <summary>
        /// 重置多线程状态（多线程注册完成后调用）
        /// </summary>
        public void ResetMultiThreadState()
        {
            try
            {
                _multiThreadManager = null;
                _multiThreadWindow = null;

                // 重置按钮状态
                StartButton.IsEnabled = true;
                ContinueButton.IsEnabled = false;
                PauseButton.IsEnabled = false;
                StopButton.IsEnabled = false;

                // 恢复配置修改功能
                SetConfigurationControlsEnabled(true);

                StatusTextBlock.Text = "多线程注册已完成，可以重新开始";
                _logService.LogInfo("多线程状态已重置");
            }
            catch (Exception ex)
            {
                _logService.LogError($"重置多线程状态失败: {ex.Message}");
            }
        }

        private void InitializeProgressBars()
        {
            try
            {
                // 操作进度区域已被注释，跳过进度条初始化
                // if (OverallProgressBar != null) OverallProgressBar.Value = 0;
                // if (CurrentStepProgressBar != null) CurrentStepProgressBar.Value = 0;
                // if (OverallProgressText != null) OverallProgressText.Text = "0%";
                // if (CurrentStepProgressText != null) CurrentStepProgressText.Text = "待开始";
                // if (ProcessingSpeedText != null) ProcessingSpeedText.Text = "0 条/分钟";
                // if (EstimatedTimeText != null) EstimatedTimeText.Text = "--:--";

                // 设置定时器
                _progressTimer.Interval = TimeSpan.FromSeconds(1);
                _progressTimer.Tick += ProgressTimer_Tick;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"InitializeProgressBars failed: {ex.Message}");
            }
        }

        private void ProgressTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                UpdateProgressBars();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ProgressTimer_Tick failed: {ex.Message}");
            }
        }

        private void UpdateProgressBars()
        {
            try
            {
                if (_totalDataCount == 0) return;

                // 操作进度区域已被注释，跳过进度条更新
                // 计算总体进度
                var overallProgress = (double)_processedCount / _totalDataCount * 100;
                // if (OverallProgressBar != null) OverallProgressBar.Value = overallProgress;
                // if (OverallProgressText != null) OverallProgressText.Text = $"{overallProgress:F1}%";

                // 计算处理速度
                var elapsed = DateTime.Now - _processingStartTime;
                if (elapsed.TotalMinutes > 0)
                {
                    var speed = _processedCount / elapsed.TotalMinutes;
                    // if (ProcessingSpeedText != null) ProcessingSpeedText.Text = $"{speed:F1} 条/分钟";

                    // 计算预计剩余时间
                    var remaining = _totalDataCount - _processedCount;
                    if (speed > 0)
                    {
                        var estimatedMinutes = remaining / speed;
                        var estimatedTime = TimeSpan.FromMinutes(estimatedMinutes);
                        // if (EstimatedTimeText != null)
                        //     EstimatedTimeText.Text = $"{estimatedTime.Hours:D2}:{estimatedTime.Minutes:D2}";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UpdateProgressBars failed: {ex.Message}");
            }
        }

        private void UpdateDataStatistics()
        {
            try
            {
                var unprocessed = _registrationDataList.Count(d => d.Status == Models.DataProcessStatus.Unprocessed);
                var pending = _registrationDataList.Count(d => d.Status == Models.DataProcessStatus.Pending);
                var processing = _registrationDataList.Count(d => d.Status == Models.DataProcessStatus.Processing);
                var completed = _registrationDataList.Count(d => d.Status == Models.DataProcessStatus.Completed);
                var failed = _registrationDataList.Count(d => d.Status == Models.DataProcessStatus.Failed);

                if (UnprocessedCountTextBlock != null) UnprocessedCountTextBlock.Text = unprocessed.ToString();
                if (PendingCountTextBlock != null) PendingCountTextBlock.Text = pending.ToString();
                if (ProcessingCountTextBlock != null) ProcessingCountTextBlock.Text = processing.ToString();
                if (CompletedCountTextBlock != null) CompletedCountTextBlock.Text = completed.ToString();
                if (FailedCountTextBlock != null) FailedCountTextBlock.Text = failed.ToString();

                // 检查是否需要重置按钮状态
                CheckAndResetButtonState(processing, pending, unprocessed);

                // 更新进度条相关数据
                _totalDataCount = _registrationDataList.Count;
                _processedCount = completed + failed;

                UpdateProgressBars();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UpdateDataStatistics failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查并重置按钮状态
        /// </summary>
        private void CheckAndResetButtonState(int processingCount, int pendingCount, int unprocessedCount)
        {
            try
            {
                // 情况1：没有正在处理或等待处理的数据，且有未处理数据，且开始按钮被禁用
                if (processingCount == 0 && pendingCount == 0 && unprocessedCount > 0 && !StartButton.IsEnabled)
                {
                    // 重置状态，启用开始注册按钮
                    StartButton.IsEnabled = true;
                    ContinueButton.IsEnabled = false;
                    PauseButton.IsEnabled = false;
                    StopButton.IsEnabled = false;
                    StatusTextBlock.Text = "就绪 - 可以开始注册";

                    // 恢复配置修改功能
                    SetConfigurationControlsEnabled(true);

                    _logService.LogInfo("检测到正在注册数据为0且有未处理数据，已重置状态并启用开始注册按钮");
                }
                // 情况2：没有任何正在处理、等待处理或未处理的数据，完全重置状态
                else if (processingCount == 0 && pendingCount == 0 && unprocessedCount == 0 && StopButton.IsEnabled)
                {
                    // 完全重置状态
                    StartButton.IsEnabled = false; // 没有数据时禁用开始按钮
                    ContinueButton.IsEnabled = false;
                    PauseButton.IsEnabled = false;
                    StopButton.IsEnabled = false;
                    StatusTextBlock.Text = "所有数据处理完成，请加载新数据";

                    // 恢复配置修改功能
                    SetConfigurationControlsEnabled(true);

                    _logService.LogInfo("检测到所有数据处理完成，已完全重置按钮状态");
                }
                // 情况3：多线程模式下，没有正在处理的数据且终止按钮启用，重置状态
                else if (processingCount == 0 && StopButton.IsEnabled && _selectedThreadCount > 1)
                {
                    // 多线程模式下，处理中数据为0就重置状态
                    StartButton.IsEnabled = true;
                    ContinueButton.IsEnabled = false;
                    PauseButton.IsEnabled = false;
                    StopButton.IsEnabled = false;
                    StatusTextBlock.Text = "多线程注册已完成，可以开始新的注册";

                    // 恢复配置修改功能
                    SetConfigurationControlsEnabled(true);

                    _logService.LogInfo("检测到多线程处理中数据为0，已重置按钮状态");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"检查按钮状态失败: {ex.Message}");
            }
        }

        private void UpdateDataStatus(RegistrationData data, Models.DataProcessStatus status, string currentStep = "")
        {
            data.Status = status;
            data.CurrentStep = currentStep;

            if (status == Models.DataProcessStatus.Processing && !data.StartTime.HasValue)
            {
                data.StartTime = DateTime.Now;
            }
            else if (status == Models.DataProcessStatus.Completed || status == Models.DataProcessStatus.Failed)
            {
                data.CompletedTime = DateTime.Now;
            }

            // 强制刷新 DataGrid 显示
            Dispatcher.Invoke(() =>
            {
                DataGrid.Items.Refresh();
                UpdateDataStatistics();
            });
        }

        /// <summary>
        /// 从多线程更新数据状态（公共方法供MultiThreadWindow调用）
        /// </summary>
        public void UpdateDataStatusFromThread(RegistrationData data, Models.DataProcessStatus status, string currentStep = "")
        {
            // 在主线程中查找对应的数据并更新状态
            Dispatcher.Invoke(() =>
            {
                var existingData = _registrationDataList.FirstOrDefault(d => d.Email == data.Email);
                if (existingData != null)
                {
                    UpdateDataStatus(existingData, status, currentStep);
                }
            });
        }

        private async void TestLocalChromeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TestLocalChromeButton.IsEnabled = false;

                // 记录按钮点击操作
                _logService.LogButtonClick("测试浏览器", $"测试浏览器启动 - 模式: {_configService.BrowserMode}");

                // 根据当前选择的模式进行测试
                bool connected = false;
                string modeText = "";
                string testMessage = "";

                if (_configService.BrowserMode == Services.BrowserMode.AdsPower)
                {
                    StatusTextBlock.Text = "正在测试AdsPower连接...";
                    modeText = "AdsPower";
                    // AdsPower测试逻辑保持不变
                    _logService.LogInfo("AdsPower模式测试功能暂未实现");
                    MessageBox.Show("AdsPower模式测试功能暂未实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }
                else if (_configService.BrowserMode == Services.BrowserMode.LocalChromeIncognito)
                {
                    StatusTextBlock.Text = "正在测试无痕Chrome启动...";
                    modeText = "无痕Chrome";
                    connected = await _automationService.ConnectToLocalChromeAsync();
                    testMessage = "无痕Chrome启动测试成功！\n\n✅ 浏览器已成功启动并连接(无痕模式)\n✅ 测试页面已打开，请验证以下内容：\n\n1. 浏览器主题是否为黑色/深色\n2. 地址栏左侧是否有无痕模式图标（帽子图标）\n3. 右上角三个点菜单是否可以正常点击\n4. 窗口大小是否为900x900像素\n\n测试窗口将保持打开状态，您可以手动关闭。";
                }
                else // LocalChromeNormal
                {
                    StatusTextBlock.Text = "正在测试默认Chrome启动...";
                    modeText = "默认Chrome";
                    connected = await _automationService.ConnectToLocalChromeNormalAsync();
                    testMessage = "默认Chrome启动测试成功！\n\n✅ 浏览器已成功启动并连接(默认模式)\n✅ 测试页面已打开，请验证以下内容：\n\n1. 浏览器主题是否为正常/浅色\n2. 地址栏左侧没有无痕模式图标\n3. 右上角三个点菜单是否可以正常点击\n4. 窗口大小是否为500x500像素\n\n测试窗口将保持打开状态，您可以手动关闭。";
                }

                if (connected)
                {
                    StatusTextBlock.Text = $"{modeText}测试成功！";
                    _logService.LogInfo($"{modeText}浏览器测试成功");
                    MessageBox.Show(testMessage, "测试成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    throw new Exception("连接失败");
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "浏览器测试失败";
                _logService.LogError($"浏览器测试失败: {ex.Message}");

                string errorMessage = $"浏览器启动失败:\n\n{ex.Message}\n\n";
                errorMessage += "解决方案:\n";
                errorMessage += "1. 安装 Google Chrome 浏览器 (推荐)\n";
                errorMessage += "2. 或者安装 Microsoft Edge 浏览器\n";
                errorMessage += "3. 确保浏览器已正确安装到系统默认位置\n";
                errorMessage += "4. 重启应用程序后重试\n\n";
                errorMessage += "如果问题持续存在，请使用 AdsPower 模式。";

                MessageBox.Show(errorMessage, "浏览器启动失败", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            finally
            {
                TestLocalChromeButton.IsEnabled = true;
            }
        }

        private void SelectFileButton_Click(object sender, RoutedEventArgs e)
        {
            // 记录按钮点击操作
            _logService.LogButtonClick("选择文件", "打开文件选择对话框");

            var openFileDialog = new OpenFileDialog
            {
                Filter = "文本文件 (*.txt)|*.txt",
                Title = "选择数据文件"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _selectedFilePath = openFileDialog.FileName;
                FilePathTextBox.Text = _selectedFilePath;
                LoadDataButton.IsEnabled = true;
                StatusTextBlock.Text = "文件已选择，请点击加载信息";

                // 记录文件选择结果
                _logService.LogInfo($"已选择文件: {_selectedFilePath}");
            }
        }

        private void LoadDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 记录按钮点击操作
                _logService.LogButtonClick("加载信息", $"加载数据文件: {_selectedFilePath}");

                var dataList = _fileService.LoadDataFromFile(_selectedFilePath);

                _registrationDataList.Clear();
                foreach (var data in dataList)
                {
                    _registrationDataList.Add(data);
                }

                // 根据浏览器模式决定启用条件
                bool canStart = _registrationDataList.Count > 0;
                if (_configService.BrowserMode == Services.BrowserMode.AdsPower)
                {
                    canStart = canStart && !string.IsNullOrEmpty(ProfileIdTextBox.Text);
                }
                StartButton.IsEnabled = canStart;
                StatusTextBlock.Text = $"成功加载 {_registrationDataList.Count} 条数据";
                _currentDataIndex = 0;

                // 更新统计信息
                UpdateDataStatistics();

                // 记录加载结果
                _logService.LogInfo($"成功加载 {_registrationDataList.Count} 条数据");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "加载数据失败";
                _logService.LogError($"加载数据失败: {ex.Message}");
            }
        }

        private async void StartButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentDataIndex >= _registrationDataList.Count)
            {
                MessageBox.Show("所有数据已处理完成", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                StartButton.IsEnabled = false;

                // 记录按钮点击操作
                _logService.LogButtonClick("开始注册", "启动注册流程");

                // 检查是否为多线程模式
                if (_selectedThreadCount > 1)
                {
                    await StartMultiThreadRegistration();
                    return;
                }

                // 单线程模式的原有逻辑

                bool connected = false;

                // 根据浏览器模式选择不同的启动方式
                if (_configService.BrowserMode == Services.BrowserMode.AdsPower)
                {
                    // AdsPower 模式
                    StatusTextBlock.Text = "正在测试Adspower连接...";

                    // 测试API连接 - 使用配置的API端点
                    var testUrl = $"{_configService.AdsApiUrl}/api/v1/user/list";
                    using var testClient = new HttpClient();
                    testClient.Timeout = TimeSpan.FromSeconds(5);

                    try
                    {
                        var testResponse = await testClient.GetAsync(testUrl);
                        // 只要能连接就行，不管返回什么状态码
                    }
                    catch (Exception)
                    {
                        throw new Exception($"无法连接到Adspower API ({_configService.AdsApiUrl})，请确保：\n1. Adspower软件已启动\n2. API服务正常运行\n3. 防火墙允许本地连接");
                    }

                    StatusTextBlock.Text = "正在启动浏览器...";

                    // 启动Adspower浏览器
                    var profileId = ProfileIdTextBox.Text.Trim();
                    StatusTextBlock.Text = $"正在启动浏览器实例: {profileId}";

                    var wsEndpoint = await _adsService.StartBrowserAsync(profileId);
                    if (string.IsNullOrEmpty(wsEndpoint))
                    {
                        throw new Exception($"无法获取浏览器连接地址，请检查实例ID '{profileId}' 是否正确");
                    }

                    StatusTextBlock.Text = $"正在连接浏览器: {wsEndpoint}";

                    // 连接到浏览器
                    connected = await _automationService.ConnectToBrowserAsync(wsEndpoint);
                    if (!connected)
                    {
                        throw new Exception($"连接浏览器失败\n实例ID: {profileId}\n连接地址: {wsEndpoint}");
                    }
                }
                else if (_configService.BrowserMode == Services.BrowserMode.LocalChromeIncognito)
                {
                    // 无痕 Chrome 模式
                    StatusTextBlock.Text = "正在启动无痕Chrome浏览器...";

                    connected = await _automationService.ConnectToLocalChromeAsync();
                    if (!connected)
                    {
                        throw new Exception("启动无痕Chrome浏览器失败，请确保系统已安装Google Chrome浏览器");
                    }
                }
                else
                {
                    // 默认 Chrome 模式
                    StatusTextBlock.Text = "正在启动默认Chrome浏览器...";

                    connected = await _automationService.ConnectToLocalChromeNormalAsync();
                    if (!connected)
                    {
                        throw new Exception("启动默认Chrome浏览器失败，请确保系统已安装Google Chrome浏览器");
                    }
                }

                // 开始注册
                var currentData = _registrationDataList[_currentDataIndex];

                // 记录注册开始（空一行分隔）
                var dataInfo = $"邮箱: {currentData.Email}, 索引: {_currentDataIndex + 1}/{_registrationDataList.Count}";
                _logService.LogRegistrationStart(dataInfo);

                // 如果数据是"未处理"状态，先改为"待处理"
                if (currentData.Status == Models.DataProcessStatus.Unprocessed)
                {
                    UpdateDataStatus(currentData, Models.DataProcessStatus.Pending, "准备开始注册");
                }

                // 清空并重置进度条
                InitializeProgressBars();
                _processedCount = 0;
                _totalDataCount = _registrationDataList.Count;
                _processingStartTime = DateTime.Now;
                _progressTimer.Start();
                // if (CurrentStepProgressText != null) CurrentStepProgressText.Text = "正在启动";

                // 更新数据状态为处理中
                UpdateDataStatus(currentData, Models.DataProcessStatus.Processing, "正在启动注册");

                // 开始注册时锁定配置
                SetConfigurationControlsEnabled(false);

                await _automationService.StartRegistrationAsync(currentData);

                PauseButton.IsEnabled = true;
                StopButton.IsEnabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动注册失败:\n\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StartButton.IsEnabled = true;
                StatusTextBlock.Text = "启动失败";
            }
        }

        private async void ContinueButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ContinueButton.IsEnabled = false;

                // 记录按钮点击操作
                _logService.LogButtonClick("继续注册", "继续执行注册流程");

                await _automationService.ContinueRegistrationAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"继续注册失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                ContinueButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// 启动多线程注册
        /// </summary>
        private async Task StartMultiThreadRegistration()
        {
            try
            {
                _logService.LogInfo($"开始启动多线程注册，线程数量: {_selectedThreadCount}");

                // 验证浏览器模式兼容性
                if (_configService.BrowserMode == Services.BrowserMode.AdsPower)
                {
                    MessageBox.Show("多线程模式不支持AdsPower浏览器，请切换到默认Chrome或无痕Chrome模式",
                        "模式不兼容", MessageBoxButton.OK, MessageBoxImage.Warning);
                    StartButton.IsEnabled = true;
                    return;
                }

                // 获取未处理的数据
                var unprocessedData = _registrationDataList
                    .Where(d => d.Status == Models.DataProcessStatus.Unprocessed || d.Status == Models.DataProcessStatus.Pending)
                    .ToList();

                if (unprocessedData.Count == 0)
                {
                    MessageBox.Show("没有可处理的数据", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    StartButton.IsEnabled = true;
                    return;
                }

                // 检查数据数量是否足够
                if (unprocessedData.Count < _selectedThreadCount)
                {
                    var result = MessageBox.Show(
                        $"当前有{unprocessedData.Count}条数据，少于选择的{_selectedThreadCount}个线程。\n是否继续使用{unprocessedData.Count}个线程？",
                        "数据数量不足", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.No)
                    {
                        StartButton.IsEnabled = true;
                        return;
                    }

                    _selectedThreadCount = Math.Min(_selectedThreadCount, unprocessedData.Count);
                }

                // 更新状态
                StatusTextBlock.Text = $"正在启动{_selectedThreadCount}个线程...";

                // 创建或重用多线程管理器
                if (_multiThreadManager == null)
                {
                    _multiThreadManager = new MultiThreadManager(_configService);
                    _logService.LogInfo("多线程管理器已重新创建");
                }

                // 启动多线程注册
                var success = await _multiThreadManager.StartMultiThreadRegistration(_selectedThreadCount, unprocessedData);

                if (success)
                {
                    // 打开多线程管理窗口
                    OpenMultiThreadWindowBtn_Click(null!, null!);

                    // 禁用相关按钮
                    PauseButton.IsEnabled = false; // 多线程模式在管理窗口中控制
                    StopButton.IsEnabled = true;
                    SetConfigurationControlsEnabled(false);

                    StatusTextBlock.Text = $"多线程注册已启动({_selectedThreadCount}个线程)";
                    _logService.LogInfo($"多线程注册启动成功，共{_selectedThreadCount}个线程");
                }
                else
                {
                    StatusTextBlock.Text = "多线程注册启动失败";
                    StartButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"启动多线程注册失败: {ex.Message}");
                MessageBox.Show($"启动多线程注册失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StartButton.IsEnabled = true;
                StatusTextBlock.Text = "多线程注册启动失败";
            }
        }



        private void PauseButton_Click(object sender, RoutedEventArgs e)
        {
            // 记录按钮点击操作
            _logService.LogButtonClick("暂停注册", "暂停当前注册流程");

            _automationService.PauseRegistration();
            PauseButton.IsEnabled = false;
            ContinueButton.IsEnabled = true;
        }

        private void StopButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 记录按钮点击操作
                _logService.LogButtonClick("终止注册", "终止当前注册流程");

                // 检查是否为多线程模式
                if (_selectedThreadCount > 1 && _multiThreadManager != null)
                {
                    // 多线程模式：停止所有线程
                    _multiThreadManager.StopAllThreads();
                    StatusTextBlock.Text = "多线程注册已终止";
                    _logService.LogRegistrationEnd("多线程注册被终止");

                    // 清理多线程相关资源
                    _multiThreadManager = null;
                    if (_multiThreadWindow != null)
                    {
                        _multiThreadWindow.Close();
                        _multiThreadWindow = null;
                    }
                }
                else
                {
                    // 单线程模式：使用原有逻辑
                    _automationService.StopRegistration();

                    // 将当前数据移动到已被终止注册数据区域
                    if (_currentDataIndex < _registrationDataList.Count)
                    {
                        var currentData = _registrationDataList[_currentDataIndex];
                        _fileService.MoveTerminatedData(_selectedFilePath, currentData);
                        _registrationDataList.RemoveAt(_currentDataIndex);
                        StatusTextBlock.Text = "注册已终止，数据已移动到已被终止注册数据列表";

                        // 记录注册结束
                        _logService.LogRegistrationEnd($"注册被终止 - 邮箱: {currentData.Email}");
                    }
                    else
                    {
                        StatusTextBlock.Text = "注册已终止";
                        _logService.LogRegistrationEnd("注册被终止");
                    }
                }

                // 重置按钮状态
                StartButton.IsEnabled = true;
                ContinueButton.IsEnabled = false;
                PauseButton.IsEnabled = false;
                StopButton.IsEnabled = false;

                // 恢复配置修改功能
                SetConfigurationControlsEnabled(true);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"终止注册失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TestConnectionButton.IsEnabled = false;

                // 记录按钮点击操作
                _logService.LogButtonClick("测试连接", $"测试AdsPower API连接: {_configService.AdsApiUrl}");

                StatusTextBlock.Text = "正在测试Adspower连接...";

                // 测试API连接 - 使用配置的API端点
                var testUrl = $"{_configService.AdsApiUrl}/api/v1/user/list";
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(5);

                var response = await client.GetAsync(testUrl);

                if (response.IsSuccessStatusCode)
                {
                    StatusTextBlock.Text = "Adspower连接成功！";
                    _logService.LogInfo($"AdsPower连接测试成功 - HTTP {response.StatusCode}");
                    MessageBox.Show("连接测试成功！\n\nAdspower API服务正常运行。", "连接成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusTextBlock.Text = $"Adspower连接失败 (HTTP {response.StatusCode})";
                    _logService.LogError($"AdsPower连接测试失败 - HTTP {response.StatusCode}");
                    MessageBox.Show($"连接测试失败！\n\nHTTP状态码: {response.StatusCode}\n请检查Adspower软件是否正常运行。", "连接失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (TaskCanceledException)
            {
                StatusTextBlock.Text = "连接超时";
                _logService.LogError($"AdsPower连接测试超时 - API地址: {_configService.AdsApiUrl}");
                MessageBox.Show($"连接测试超时！\n\n请检查:\n1. Adspower软件是否已启动\n2. API地址是否正确: {_configService.AdsApiUrl}\n3. 防火墙是否阻止连接", "连接超时", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "连接失败";
                _logService.LogError($"AdsPower连接测试失败: {ex.Message}");
                MessageBox.Show($"连接测试失败！\n\n错误详情: {ex.Message}\n\n请检查:\n1. Adspower软件是否已启动\n2. API服务是否正常运行", "连接失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                TestConnectionButton.IsEnabled = true;
            }
        }



        private void OnStatusChanged(string status)
        {
            Dispatcher.Invoke(() =>
            {
                StatusTextBlock.Text = status;
                // 记录系统状态变化到日志
                _logService.LogStatusChange(status);
            });
        }

        private void OnRegistrationStatusChanged(RegistrationStatus status)
        {
            Dispatcher.Invoke(() =>
            {
                switch (status)
                {
                    case RegistrationStatus.WaitingForVerification:
                    case RegistrationStatus.WaitingForPhoneInput:
                    case RegistrationStatus.WaitingForSMSVerification:
                    case RegistrationStatus.WaitingForAddressCorrection:
                        // 显示窗口到前台
                        WindowState = WindowState.Normal;
                        Activate();
                        Topmost = true;
                        Topmost = false;
                        ContinueButton.IsEnabled = true;
                        PauseButton.IsEnabled = true; // 等待验证码时应该允许暂停
                        StopButton.IsEnabled = true;

                        // 设置状态文本为红色，表示需要手动处理
                        StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(229, 62, 62)); // 红色
                        StatusTextBlock.Text = "⚠️ 等待手动处理 - 请完成验证后点击继续注册";

                        // 注册进行中，禁用配置修改
                        SetConfigurationControlsEnabled(false);
                        break;

                    case RegistrationStatus.Running:
                        ContinueButton.IsEnabled = false;
                        // 移除直接设置背景色，让样式系统处理
                        PauseButton.IsEnabled = true;
                        StopButton.IsEnabled = true;

                        // 恢复状态文本为正常颜色
                        StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(74, 85, 104)); // 正常灰色
                        StatusTextBlock.Text = "正在运行注册流程...";

                        // 注册进行中，禁用配置修改
                        SetConfigurationControlsEnabled(false);

                        // 更新当前数据状态
                        if (_currentDataIndex < _registrationDataList.Count)
                        {
                            UpdateDataStatus(_registrationDataList[_currentDataIndex], Models.DataProcessStatus.Processing, "正在处理");
                        }
                        break;

                    case RegistrationStatus.Paused:
                        // 显示窗口到前台并高亮继续按钮
                        WindowState = WindowState.Normal;
                        Activate();
                        Topmost = true;
                        Topmost = false;
                        ContinueButton.IsEnabled = true;
                        // 移除直接设置背景色，让样式系统处理高亮
                        PauseButton.IsEnabled = false;
                        StopButton.IsEnabled = true;

                        // 恢复状态文本为正常颜色
                        StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(74, 85, 104)); // 正常灰色
                        StatusTextBlock.Text = "注册已暂停 - 点击继续注册按钮恢复";

                        // 暂停状态仍然禁用配置修改（因为注册还没完成）
                        SetConfigurationControlsEnabled(false);
                        break;

                    case RegistrationStatus.Completed:
                        // 注册完成状态现在通过DataCompletedWithoutKeys事件处理
                        // 这里只需要更新UI状态
                        StatusTextBlock.Text = "注册完成，正在处理数据...";

                        ContinueButton.IsEnabled = false;
                        PauseButton.IsEnabled = false;
                        StopButton.IsEnabled = false;

                        // 注册完成，恢复配置修改功能
                        SetConfigurationControlsEnabled(true);
                        break;

                    case RegistrationStatus.Idle:
                        // 空闲状态：启用开始注册按钮
                        StartButton.IsEnabled = _registrationDataList.Count > 0;
                        ContinueButton.IsEnabled = false;
                        PauseButton.IsEnabled = false;
                        StopButton.IsEnabled = false;

                        // 空闲状态，恢复配置修改功能
                        SetConfigurationControlsEnabled(true);
                        break;

                    case RegistrationStatus.Error:
                        // 记录错误状态
                        if (_currentDataIndex < _registrationDataList.Count)
                        {
                            var errorData = _registrationDataList[_currentDataIndex];
                            _logService.LogError($"注册过程出现错误 - 邮箱: {errorData.Email}");
                        }
                        else
                        {
                            _logService.LogError("注册过程出现错误");
                        }

                        // 显示窗口到前台并高亮继续按钮
                        WindowState = WindowState.Normal;
                        Activate();
                        Topmost = true;
                        Topmost = false;

                        // 错误状态时禁用开始注册，启用继续注册
                        StartButton.IsEnabled = false;
                        ContinueButton.IsEnabled = true;
                        PauseButton.IsEnabled = false;
                        StopButton.IsEnabled = true;

                        // 错误状态仍然禁用配置修改（因为注册还没完成）
                        SetConfigurationControlsEnabled(false);
                        break;

                    case RegistrationStatus.Terminated:
                        // 终止状态：恢复初始按钮状态
                        // 注意：如果数据已经通过SaveFailedData事件被处理（如支付信息错误），
                        // 则不需要再次处理数据，因为数据已经被移动到失败区域

                        // 检查数据是否还在列表中（如果已被SaveFailedData处理，则已被移除）
                        if (_currentDataIndex < _registrationDataList.Count)
                        {
                            var currentData = _registrationDataList[_currentDataIndex];
                            if (currentData.Status == Models.DataProcessStatus.Processing)
                            {
                                // 只有在数据状态为处理中时才标记为失败（手动终止的情况）
                                // 如果是支付信息错误等自动失败，数据已经被SaveFailedData处理
                                UpdateDataStatus(currentData, Models.DataProcessStatus.Failed, "手动终止");
                            }
                        }

                        StartButton.IsEnabled = true;
                        ContinueButton.IsEnabled = false;
                        PauseButton.IsEnabled = false;
                        StopButton.IsEnabled = false;
                        StatusTextBlock.Text = "注册已终止";

                        // 终止后，恢复配置修改功能
                        SetConfigurationControlsEnabled(true);
                        break;
                }
            });
        }

        private void OnShowInputDialog(string title, string message, bool isManualMode)
        {
            Dispatcher.Invoke(() =>
            {
                // 注册失败的弹窗总是显示，不受状态限制
                bool isFailureDialog = title.Contains("注册失败") || title.Contains("失败");

                // 检查当前状态，如果已经终止且不是失败弹窗则不显示对话框
                if (_automationService.Status == RegistrationStatus.Terminated && !isFailureDialog)
                {
                    return;
                }

                // 只有手动模式或失败弹窗才显示窗口和弹窗
                if (isManualMode || isFailureDialog)
                {
                    // 将主窗口居中并置于前台
                    CenterAndActivateWindow();

                    // 根据弹窗类型选择图标
                    var icon = isFailureDialog ? MessageBoxImage.Error : MessageBoxImage.Information;
                    MessageBox.Show(this, message, title, MessageBoxButton.OK, icon);
                }
                // 自动模式不显示弹窗，不将窗口置于前台
            });
        }

        private void CenterAndActivateWindow()
        {
            try
            {
                // 将窗口居中显示
                this.WindowStartupLocation = WindowStartupLocation.CenterScreen;

                // 如果窗口最小化，先恢复
                if (this.WindowState == WindowState.Minimized)
                {
                    this.WindowState = WindowState.Normal;
                }

                // 激活窗口并置于前台
                this.Activate();
                this.Topmost = true;
                this.Topmost = false; // 立即取消置顶，避免一直在最前面
                this.Focus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"窗口居中失败: {ex.Message}");
            }
        }

        public void OnSaveFailedData(string failureReason, RegistrationData? failedData)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    if (!string.IsNullOrEmpty(_selectedFilePath))
                    {
                        // 如果传入了具体的失败数据，直接使用
                        if (failedData != null)
                        {
                            // 在列表中查找对应的数据
                            var existingData = _registrationDataList.FirstOrDefault(d => d.Email == failedData.Email);
                            if (existingData != null)
                            {
                                // 使用FileService的方法来保存失败数据
                                SaveFailedDataToFile(_selectedFilePath, existingData, failureReason);

                                // 更新数据状态为失败
                                UpdateDataStatus(existingData, Models.DataProcessStatus.Failed, $"失败: {failureReason}");

                                // 从列表中移除失败的数据
                                _registrationDataList.Remove(existingData);

                                // 刷新显示
                                DataGrid.Items.Refresh();
                                UpdateDataStatistics();

                                _logService.LogInfo($"已处理失败数据: {existingData.Email}, 失败原因: {failureReason}");
                            }
                            else
                            {
                                _logService.LogError($"在数据列表中未找到失败的数据: {failedData.Email}, 失败原因: {failureReason}");
                            }
                        }
                        else
                        {
                            // 兼容旧版本：如果没有传入具体数据，使用原有逻辑
                            if (_selectedThreadCount > 1)
                            {
                                // 多线程模式：查找状态为Processing的数据
                                var processingData = _registrationDataList.FirstOrDefault(d => d.Status == Models.DataProcessStatus.Processing);
                                if (processingData != null)
                                {
                                    SaveFailedDataToFile(_selectedFilePath, processingData, failureReason);
                                    UpdateDataStatus(processingData, Models.DataProcessStatus.Failed, $"失败: {failureReason}");
                                    _registrationDataList.Remove(processingData);
                                    DataGrid.Items.Refresh();
                                    UpdateDataStatistics();
                                    _logService.LogInfo($"已处理失败数据: {processingData.Email}, 失败原因: {failureReason}");
                                }
                                else
                                {
                                    _logService.LogError($"多线程模式下无法找到失败的数据，失败原因: {failureReason}");
                                }
                            }
                            else
                            {
                                // 单线程模式：使用_currentDataIndex
                                if (_currentDataIndex < _registrationDataList.Count)
                                {
                                    var currentData = _registrationDataList[_currentDataIndex];
                                    SaveFailedDataToFile(_selectedFilePath, currentData, failureReason);
                                    UpdateDataStatus(currentData, Models.DataProcessStatus.Failed, $"失败: {failureReason}");
                                    _registrationDataList.RemoveAt(_currentDataIndex);
                                    DataGrid.Items.Refresh();
                                    UpdateDataStatistics();
                                    _logService.LogInfo($"已处理失败数据: {currentData.Email}, 失败原因: {failureReason}");
                                }
                                else
                                {
                                    _logService.LogError($"单线程模式下_currentDataIndex超出范围，失败原因: {failureReason}");
                                }
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logService.LogError($"保存失败数据时出错: {ex.Message}");
            }
        }

        private void SaveFailedDataToFile(string filePath, RegistrationData failedData, string failureReason)
        {
            try
            {
                var lines = File.ReadAllLines(filePath).ToList();
                var dataLine = failedData.ToLine();

                // 移除原数据行
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == dataLine)
                    {
                        lines.RemoveAt(i);
                        break;
                    }
                }

                // 查找或创建"注册失败数据："标记
                var failedDataIndex = -1;
                for (int i = 0; i < lines.Count; i++)
                {
                    if (lines[i].Trim() == "注册失败数据：")
                    {
                        failedDataIndex = i;
                        break;
                    }
                }

                if (failedDataIndex == -1)
                {
                    // 如果没有找到标记，在文件末尾添加
                    lines.Add("");
                    lines.Add("注册失败数据：");
                    lines.Add($"{dataLine} // 失败原因: {failureReason}");
                }
                else
                {
                    // 在标记后添加数据
                    lines.Insert(failedDataIndex + 1, $"{dataLine} // 失败原因: {failureReason}");
                }

                File.WriteAllLines(filePath, lines);
            }
            catch (Exception ex)
            {
                throw new Exception($"保存失败数据到文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理数据完成事件（包含密钥的成功注册）
        /// </summary>
        public void OnDataCompleted(RegistrationData completedData)
        {
            try
            {
                // 使用同步方式确保所有操作完成
                if (Dispatcher.CheckAccess())
                {
                    // 已在UI线程中，直接执行
                    ProcessDataCompletion(completedData);
                }
                else
                {
                    // 不在UI线程中，同步调用Dispatcher
                    Dispatcher.Invoke(() => ProcessDataCompletion(completedData));
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理数据完成事件失败: {ex.Message}");
                throw; // 重新抛出异常，让调用方知道操作失败
            }
        }

        /// <summary>
        /// 处理数据完成事件（统一移动到注册成功区域）
        /// </summary>
        public void OnDataCompletedWithoutKeys(RegistrationData completedData)
        {
            try
            {
                // 使用同步方式确保所有操作完成
                if (Dispatcher.CheckAccess())
                {
                    // 已在UI线程中，直接执行
                    ProcessDataCompletion(completedData);
                }
                else
                {
                    // 不在UI线程中，同步调用Dispatcher
                    Dispatcher.Invoke(() => ProcessDataCompletion(completedData));
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理数据完成事件失败: {ex.Message}");
                throw; // 重新抛出异常，让调用方知道操作失败
            }
        }

        /// <summary>
        /// 处理数据完成的具体逻辑
        /// </summary>
        private void ProcessDataCompletion(RegistrationData completedData)
        {
            try
            {
                _logService.LogInfo($"开始处理数据完成事件: {completedData.Email}");

                // 将完成的数据移动到"注册成功："区域（无论是否包含密钥）
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    _fileService.MoveSuccessData(_selectedFilePath, completedData);
                    _logService.LogInfo($"已将完成数据移动到注册成功区域: {completedData.Email}");
                }

                // 从当前列表中移除已完成的数据
                var dataToRemove = _registrationDataList.FirstOrDefault(d => d.Email == completedData.Email);
                if (dataToRemove != null)
                {
                    _registrationDataList.Remove(dataToRemove);
                    _logService.LogInfo($"已完成数据移除: {completedData.Email}");
                }

                // 更新界面显示
                DataGrid.Items.Refresh();
                UpdateDataStatistics();

                _logService.LogInfo($"数据完成事件处理完毕: {completedData.Email}");
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理数据完成具体逻辑失败: {ex.Message}");
                throw; // 重新抛出异常
            }
        }

        /// <summary>
        /// 处理账单问题数据完成事件（移动到注册成功区域并添加账单标记）
        /// </summary>
        public void OnDataCompletedWithBillingIssue(RegistrationData completedData)
        {
            try
            {
                // 使用同步方式确保所有操作完成
                if (Dispatcher.CheckAccess())
                {
                    // 已在UI线程中，直接执行
                    ProcessBillingIssueDataCompletion(completedData);
                }
                else
                {
                    // 不在UI线程中，同步调用Dispatcher
                    Dispatcher.Invoke(() => ProcessBillingIssueDataCompletion(completedData));
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理账单问题数据完成事件失败: {ex.Message}");
                throw; // 重新抛出异常，让调用方知道操作失败
            }
        }

        /// <summary>
        /// 处理账单问题数据完成的具体逻辑
        /// </summary>
        private void ProcessBillingIssueDataCompletion(RegistrationData completedData)
        {
            try
            {
                _logService.LogInfo($"开始处理账单问题数据完成事件: {completedData.Email}");

                // 将账单问题数据移动到"注册成功："区域（添加账单标记）
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    _fileService.MoveBillingIssueData(_selectedFilePath, completedData);
                    _logService.LogInfo($"已将账单问题数据移动到注册成功区域: {completedData.Email}");
                }

                // 从当前列表中移除已完成的数据
                var dataToRemove = _registrationDataList.FirstOrDefault(d => d.Email == completedData.Email);
                if (dataToRemove != null)
                {
                    _registrationDataList.Remove(dataToRemove);
                    _logService.LogInfo($"已完成账单问题数据移除: {completedData.Email}");
                }

                // 更新界面显示
                DataGrid.Items.Refresh();
                UpdateDataStatistics();

                _logService.LogInfo($"账单问题数据完成事件处理完毕: {completedData.Email}");
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理账单问题数据完成具体逻辑失败: {ex.Message}");
                throw; // 重新抛出异常
            }
        }

        /// <summary>
        /// 处理无资格问题数据完成事件（移动到注册成功区域并添加无资格标记）
        /// </summary>
        public void OnDataCompletedWithIneligibleIssue(RegistrationData completedData)
        {
            try
            {
                // 使用同步方式确保所有操作完成
                if (Dispatcher.CheckAccess())
                {
                    // 已在UI线程中，直接执行
                    ProcessIneligibleIssueDataCompletion(completedData);
                }
                else
                {
                    // 不在UI线程中，同步调用Dispatcher
                    Dispatcher.Invoke(() => ProcessIneligibleIssueDataCompletion(completedData));
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理无资格问题数据完成事件失败: {ex.Message}");
                throw; // 重新抛出异常，让调用方知道操作失败
            }
        }

        /// <summary>
        /// 处理无资格问题数据完成的具体逻辑
        /// </summary>
        private void ProcessIneligibleIssueDataCompletion(RegistrationData completedData)
        {
            try
            {
                _logService.LogInfo($"开始处理无资格问题数据完成事件: {completedData.Email}");

                // 将无资格问题数据移动到"注册成功："区域（添加无资格标记）
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    _fileService.MoveIneligibleIssueData(_selectedFilePath, completedData);
                    _logService.LogInfo($"已将无资格问题数据移动到注册成功区域: {completedData.Email}");
                }

                // 从当前列表中移除已完成的数据
                var dataToRemove = _registrationDataList.FirstOrDefault(d => d.Email == completedData.Email);
                if (dataToRemove != null)
                {
                    _registrationDataList.Remove(dataToRemove);
                    _logService.LogInfo($"已完成无资格问题数据移除: {completedData.Email}");
                }

                // 更新界面显示
                DataGrid.Items.Refresh();
                UpdateDataStatistics();

                _logService.LogInfo($"无资格问题数据完成事件处理完毕: {completedData.Email}");
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理无资格问题数据完成具体逻辑失败: {ex.Message}");
                throw; // 重新抛出异常
            }
        }

        /// <summary>
        /// 处理移除已完成数据事件（仅移除，不移动到文件）
        /// </summary>
        private void OnRemoveCompletedData(RegistrationData completedData)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 从当前列表中移除已完成的数据
                    var dataToRemove = _registrationDataList.FirstOrDefault(d => d.Email == completedData.Email);
                    if (dataToRemove != null)
                    {
                        _registrationDataList.Remove(dataToRemove);
                        _logService.LogInfo($"已移除完成数据: {completedData.Email}");
                    }

                    // 更新界面显示
                    DataGrid.Items.Refresh();
                    UpdateDataStatistics();
                });
            }
            catch (Exception ex)
            {
                _logService.LogError($"移除完成数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理保存剪贴板信息到成功数据区域事件
        /// </summary>
        public void OnSaveClipboardInfoToSuccessData(string clipboardContent)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 保存剪贴板信息到"成功数据："区域
                    if (!string.IsNullOrEmpty(_selectedFilePath))
                    {
                        _fileService.SaveClipboardInfoToSuccessData(_selectedFilePath, clipboardContent);
                        _logService.LogInfo($"已将剪贴板信息保存到成功数据区域: {clipboardContent}");
                    }
                });
            }
            catch (Exception ex)
            {
                _logService.LogError($"保存剪贴板信息到成功数据区域失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存当前数据列表到文件
        /// </summary>
        private void SaveCurrentDataToFile()
        {
            try
            {
                if (string.IsNullOrEmpty(_selectedFilePath))
                {
                    _logService.LogError("无法保存数据：未选择文件路径");
                    return;
                }

                // 将当前数据列表转换为文本行
                var lines = new List<string>();
                foreach (var data in _registrationDataList)
                {
                    lines.Add(data.ToLine());
                }

                // 写入文件
                File.WriteAllLines(_selectedFilePath, lines);
                _logService.LogInfo($"已保存 {_registrationDataList.Count} 条数据到文件");
            }
            catch (Exception ex)
            {
                _logService.LogError($"保存数据到文件失败: {ex.Message}");
            }
        }

        // 移除OnDataCompletedToStep4方法，因为我们只在完全注册成功时才移动数据
        // private void OnDataCompletedToStep4(RegistrationData completedData)
        // {
        //     // 此方法已被移除，避免在注册未完成时就移动数据
        // }

        private void InitializePhoneApiConfig()
        {
            try
            {
                // 初始化搜索功能
                InitializeCountrySearch();

                // 加载配置
                var phoneConfig = _configService.PhoneApi;

                // 根据配置设置模式选择
                switch (phoneConfig.Provider)
                {
                    case PhoneProvider.Durian:
                        if (PhoneDurianModeRadio != null) PhoneDurianModeRadio.IsChecked = true;
                        if (PhoneModeText != null) PhoneModeText.Text = "榴莲";
                        if (PhoneModeIndicator != null) PhoneModeIndicator.Fill = Brushes.Green;
                        break;
                    case PhoneProvider.Qianchuan:
                        if (PhoneQianchuanModeRadio != null) PhoneQianchuanModeRadio.IsChecked = true;
                        if (PhoneModeText != null) PhoneModeText.Text = "千川";
                        if (PhoneModeIndicator != null) PhoneModeIndicator.Fill = Brushes.Purple;
                        break;
                    case PhoneProvider.USA:
                        if (PhoneUsaModeRadio != null) PhoneUsaModeRadio.IsChecked = true;
                        if (PhoneModeText != null) PhoneModeText.Text = "美国";
                        if (PhoneModeIndicator != null) PhoneModeIndicator.Fill = Brushes.Blue;
                        break;
                    case PhoneProvider.Manual:
                        if (PhoneManualModeRadio != null) PhoneManualModeRadio.IsChecked = true;
                        if (PhoneModeText != null) PhoneModeText.Text = "手动模式";
                        if (PhoneModeIndicator != null) PhoneModeIndicator.Fill = Brushes.Orange;
                        break;
                }

                PhoneApiNameTextBox.Text = phoneConfig.Name;
                PhoneApiKeyTextBox.Text = phoneConfig.ApiKey;
                PhoneApiProjectIdTextBox.Text = phoneConfig.ProjectId;

                // 选择当前国家
                var currentCountry = _allCountries.FirstOrDefault(c => c.Code == phoneConfig.CountryCode);
                if (currentCountry != null)
                {
                    SelectCountry(currentCountry);
                }

                // 设置界面可见性（只有榴莲模式显示配置界面）
                UpdatePhoneApiConfigVisibility(phoneConfig.Provider == PhoneProvider.Durian);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"InitializePhoneApiConfig failed: {ex.Message}");
            }
        }

        private void InitializeEmailVerificationConfig()
        {
            try
            {
                var emailConfig = _configService.EmailVerification;

                // 设置单选按钮状态
                if (emailConfig.AutoMode)
                {
                    // 根据Provider设置对应的单选按钮
                    if (emailConfig.Provider == "Microsoft")
                    {
                        if (EmailMicrosoftRadio != null) EmailMicrosoftRadio.IsChecked = true;
                    }
                    else // 默认为Google
                    {
                        if (EmailGoogleRadio != null) EmailGoogleRadio.IsChecked = true;
                    }
                }
                else
                {
                    if (EmailManualModeRadio != null) EmailManualModeRadio.IsChecked = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"InitializeEmailVerificationConfig failed: {ex.Message}");
            }
        }

        private void InitializeCaptchaRecognitionConfig()
        {
            try
            {
                var captchaConfig = _configService.CaptchaRecognition;

                // 设置单选按钮状态
                if (captchaConfig.AutoMode)
                {
                    // 自动模式，根据Provider选择对应的按钮
                    if (captchaConfig.Provider == "YesCaptcha")
                    {
                        if (CaptchaYesModeRadio != null) CaptchaYesModeRadio.IsChecked = true;
                    }
                    else
                    {
                        if (CaptchaCloudModeRadio != null) CaptchaCloudModeRadio.IsChecked = true;
                    }
                }
                else
                {
                    if (CaptchaManualModeRadio != null) CaptchaManualModeRadio.IsChecked = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"InitializeCaptchaRecognitionConfig failed: {ex.Message}");
            }
        }

        private void CheckEmailVerificationSoftware()
        {
            try
            {
                // 检查是否有AWS验证码.exe进程在运行
                var emailProcesses = System.Diagnostics.Process.GetProcessesByName("AWS验证码");

                if (emailProcesses.Length > 0)
                {
                    StatusTextBlock.Text = "检测到邮箱验证码软件正在运行，自动模式可用";
                    System.Diagnostics.Debug.WriteLine("检测到AWS验证码.exe正在运行");
                }
                else
                {
                    StatusTextBlock.Text = "未检测到邮箱验证码软件，请先启动AWS验证码.exe";
                    System.Diagnostics.Debug.WriteLine("未检测到AWS验证码.exe进程");
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "检查邮箱验证码软件状态失败";
                System.Diagnostics.Debug.WriteLine($"CheckEmailVerificationSoftware failed: {ex.Message}");
            }
        }

        private void EmailModeRadio_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender == EmailMicrosoftRadio && EmailMicrosoftRadio?.IsChecked == true)
                {
                    _configService.UpdateEmailVerificationConfig(true);
                    _configService.UpdateEmailVerificationProvider("Microsoft");
                    StatusTextBlock.Text = "邮箱验证码已切换到微软模式";
                }
                else if (sender == EmailGoogleRadio && EmailGoogleRadio?.IsChecked == true)
                {
                    _configService.UpdateEmailVerificationConfig(true);
                    _configService.UpdateEmailVerificationProvider("Google");
                    StatusTextBlock.Text = "邮箱验证码已切换到谷歌模式";
                }
                else if (sender == EmailManualModeRadio && EmailManualModeRadio?.IsChecked == true)
                {
                    _configService.UpdateEmailVerificationConfig(false);
                    _configService.UpdateEmailVerificationProvider("Manual");
                    StatusTextBlock.Text = "邮箱验证码已切换到手动输入模式";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"EmailModeRadio_Checked error: {ex.Message}");
            }
        }

        private void CaptchaYesModeRadio_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                _configService.UpdateCaptchaRecognitionConfig(true);
                _configService.UpdateCaptchaRecognitionProvider("YesCaptcha");
                StatusTextBlock.Text = "图形验证码已切换到Yes打码模式";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CaptchaYesModeRadio_Checked error: {ex.Message}");
            }
        }

        private void CaptchaCloudModeRadio_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                _configService.UpdateCaptchaRecognitionConfig(true);
                _configService.UpdateCaptchaRecognitionProvider("CloudCaptcha");
                StatusTextBlock.Text = "图形验证码已切换到云打码模式";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CaptchaCloudModeRadio_Checked error: {ex.Message}");
            }
        }

        private void CaptchaManualModeRadio_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                _configService.UpdateCaptchaRecognitionConfig(false);
                StatusTextBlock.Text = "图形验证码已切换到手动完成模式";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CaptchaManualModeRadio_Checked error: {ex.Message}");
            }
        }

        private void UpdatePhoneApiConfigVisibility(bool enabled)
        {
            var visibility = enabled ? Visibility.Visible : Visibility.Collapsed;
            if (PhoneApiConfigGrid != null) PhoneApiConfigGrid.Visibility = visibility;
            if (PhoneApiConfigGrid2 != null) PhoneApiConfigGrid2.Visibility = visibility;
            if (PhoneApiConfigGrid3 != null) PhoneApiConfigGrid3.Visibility = visibility;
        }

        private void PhoneModeRadio_Checked(object sender, RoutedEventArgs e)
        {
            // 确保所有控件都已初始化
            if (PhoneApiConfigGrid == null || StatusTextBlock == null || _configService == null)
                return;

            // 确保发送者不为空
            if (sender == null)
                return;

            try
            {
                if (sender == PhoneDurianModeRadio && PhoneDurianModeRadio?.IsChecked == true)
                {
                    // 切换到榴莲模式
                    UpdatePhoneApiConfigVisibility(true);
                    _configService.UpdatePhoneApiProvider(PhoneProvider.Durian);

                    // 刷新AutomationService中的PhoneApiService
                    _automationService.RefreshPhoneApiService();

                    StatusTextBlock.Text = "已切换到榴莲手机号码模式";

                    // 更新模式指示器
                    if (PhoneModeText != null) PhoneModeText.Text = "榴莲";
                    if (PhoneModeIndicator != null) PhoneModeIndicator.Fill = Brushes.Green;
                }
                else if (sender == PhoneQianchuanModeRadio && PhoneQianchuanModeRadio?.IsChecked == true)
                {
                    // 切换到千川模式
                    UpdatePhoneApiConfigVisibility(false); // 千川模式不需要配置界面，使用config.json中的配置
                    _configService.UpdatePhoneApiProvider(PhoneProvider.Qianchuan);

                    // 刷新AutomationService中的PhoneApiService
                    _automationService.RefreshPhoneApiService();

                    StatusTextBlock.Text = "已切换到千川手机号码模式";

                    // 更新模式指示器
                    if (PhoneModeText != null) PhoneModeText.Text = "千川";
                    if (PhoneModeIndicator != null) PhoneModeIndicator.Fill = Brushes.Purple;
                }
                else if (sender == PhoneUsaModeRadio && PhoneUsaModeRadio?.IsChecked == true)
                {
                    // 切换到美国模式
                    UpdatePhoneApiConfigVisibility(false); // 美国模式不需要配置界面
                    _configService.UpdatePhoneApiProvider(PhoneProvider.USA);

                    // 刷新AutomationService中的PhoneApiService
                    _automationService.RefreshPhoneApiService();

                    StatusTextBlock.Text = "已切换到美国手机号码模式";

                    // 更新模式指示器
                    if (PhoneModeText != null) PhoneModeText.Text = "美国";
                    if (PhoneModeIndicator != null) PhoneModeIndicator.Fill = Brushes.Blue;
                }
                else if (sender == PhoneManualModeRadio && PhoneManualModeRadio?.IsChecked == true)
                {
                    // 切换到手动模式
                    UpdatePhoneApiConfigVisibility(false);
                    _configService.UpdatePhoneApiProvider(PhoneProvider.Manual);

                    // 刷新AutomationService中的PhoneApiService
                    _automationService.RefreshPhoneApiService();

                    StatusTextBlock.Text = "已切换到手动输入手机号码模式";

                    // 更新模式指示器
                    if (PhoneModeText != null) PhoneModeText.Text = "手动模式";
                    if (PhoneModeIndicator != null) PhoneModeIndicator.Fill = Brushes.Orange;
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "切换手机号码模式失败";
                MessageBox.Show($"切换手机号码模式失败: {ex.Message}", "切换失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<CountryCodeService.CountryInfo> _allCountries = new();
        private CountryCodeService.CountryInfo? _selectedCountry = null;
        private bool _isSearchMode = false;
        private string _placeholderText = "点击搜索国家...";

        private void InitializeCountrySearch()
        {
            try
            {
                _allCountries = CountryCodeService.GetSupportedCountries();
                CountrySearchTextBox.Text = _placeholderText;
                CountrySearchTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#A0AEC0"));

                // 添加全局点击事件来关闭Popup
                this.PreviewMouseDown += MainWindow_PreviewMouseDown;

                System.Diagnostics.Debug.WriteLine($"初始化搜索功能，加载了 {_allCountries.Count} 个国家");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"InitializeCountrySearch error: {ex.Message}");
            }
        }

        private void MainWindow_PreviewMouseDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                if (SearchResultsPopup.IsOpen)
                {
                    // 检查点击是否在搜索相关控件内
                    var hitElement = e.OriginalSource as DependencyObject;
                    bool clickedInside = false;

                    if (hitElement != null)
                    {
                        clickedInside = IsElementChildOf(hitElement, CountrySearchTextBox) ||
                                       IsElementChildOf(hitElement, SearchResultsPopup.Child);
                    }

                    if (!clickedInside)
                    {
                        ExitSearchMode();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"MainWindow_PreviewMouseDown error: {ex.Message}");
            }
        }

        private void CountrySearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            try
            {
                // 进入搜索模式：清空内容，显示搜索结果
                _isSearchMode = true;
                CountrySearchTextBox.Text = "";
                CountrySearchTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#000000"));

                // 显示所有国家
                SearchResultsListBox.ItemsSource = _allCountries;
                SearchResultsPopup.IsOpen = true;

                System.Diagnostics.Debug.WriteLine($"进入搜索模式，国家数量: {_allCountries.Count}, Popup状态: {SearchResultsPopup.IsOpen}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CountrySearchTextBox_GotFocus error: {ex.Message}");
            }
        }

        private void CountrySearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            try
            {
                // 延迟处理，允许用户点击搜索结果
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    // 检查焦点是否在搜索相关控件内
                    var focusedElement = Keyboard.FocusedElement;
                    bool shouldClose = true;

                    if (focusedElement != null)
                    {
                        // 如果焦点在搜索框或列表内，不关闭
                        if (focusedElement == CountrySearchTextBox ||
                            IsElementChildOf(focusedElement as DependencyObject, SearchResultsListBox))
                        {
                            shouldClose = false;
                        }
                    }

                    if (shouldClose)
                    {
                        ExitSearchMode();
                    }

                    System.Diagnostics.Debug.WriteLine($"LostFocus: shouldClose={shouldClose}, focusedElement={focusedElement?.GetType().Name}");
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CountrySearchTextBox_LostFocus error: {ex.Message}");
            }
        }

        private bool IsElementChildOf(DependencyObject child, DependencyObject parent)
        {
            if (child == null || parent == null) return false;

            DependencyObject current = child;
            while (current != null)
            {
                if (current == parent) return true;
                current = VisualTreeHelper.GetParent(current);
            }
            return false;
        }

        private void CountrySearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (!_isSearchMode) return;

                var searchText = CountrySearchTextBox.Text?.ToLower() ?? "";

                if (string.IsNullOrEmpty(searchText))
                {
                    // 显示所有国家
                    SearchResultsListBox.ItemsSource = _allCountries;
                }
                else
                {
                    // 搜索匹配的国家
                    var filteredCountries = _allCountries.Where(country =>
                        country.Name.ToLower().Contains(searchText) ||
                        country.Code.ToLower().Contains(searchText) ||
                        country.PhoneCode.Contains(searchText)
                    ).ToList();

                    SearchResultsListBox.ItemsSource = filteredCountries;
                }

                // 确保搜索结果可见
                SearchResultsPopup.IsOpen = true;

                System.Diagnostics.Debug.WriteLine($"搜索: '{searchText}', 找到 {((IEnumerable<CountryCodeService.CountryInfo>)SearchResultsListBox.ItemsSource).Count()} 个结果");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CountrySearchTextBox_TextChanged error: {ex.Message}");
            }
        }

        private void CountrySearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.Key == Key.Escape)
                {
                    // ESC键退出搜索模式
                    ExitSearchMode();
                    CountrySearchTextBox.MoveFocus(new TraversalRequest(FocusNavigationDirection.Next));
                }
                else if (e.Key == Key.Enter)
                {
                    // 回车键选择第一个结果
                    if (SearchResultsListBox.Items.Count > 0)
                    {
                        SearchResultsListBox.SelectedIndex = 0;
                        SelectCountry(SearchResultsListBox.SelectedItem as CountryCodeService.CountryInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CountrySearchTextBox_KeyDown error: {ex.Message}");
            }
        }

        private void SearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (SearchResultsListBox.SelectedItem is CountryCodeService.CountryInfo selectedCountry)
                {
                    SelectCountry(selectedCountry);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SearchResultsListBox_SelectionChanged error: {ex.Message}");
            }
        }

        private void SelectCountry(CountryCodeService.CountryInfo? country)
        {
            try
            {
                if (country != null)
                {
                    _selectedCountry = country;

                    // 显示完整的国家名称
                    CountrySearchTextBox.Text = country.Name;
                    CountrySearchTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#000000"));

                    System.Diagnostics.Debug.WriteLine($"选择了国家: {country.Name} ({country.Code})");
                }

                // 退出搜索模式
                ExitSearchMode();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SelectCountry error: {ex.Message}");
            }
        }

        private void ExitSearchMode()
        {
            try
            {
                _isSearchMode = false;
                SearchResultsPopup.IsOpen = false;

                // 如果没有选择国家，恢复占位符或之前选择的国家
                if (_selectedCountry == null)
                {
                    CountrySearchTextBox.Text = _placeholderText;
                    CountrySearchTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#A0AEC0"));
                }
                else
                {
                    // 恢复之前选择的国家
                    CountrySearchTextBox.Text = _selectedCountry.Name;
                    CountrySearchTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#000000"));
                }

                System.Diagnostics.Debug.WriteLine("退出搜索模式");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ExitSearchMode error: {ex.Message}");
            }
        }

        // 获取当前选中的国家（供其他方法使用）
        public CountryCodeService.CountryInfo? GetSelectedCountry()
        {
            return _selectedCountry;
        }

        // 获取当前选中的国家代码（供其他方法使用）
        public string GetSelectedCountryCode()
        {
            return _selectedCountry?.Code ?? "";
        }





        private async void TestPhoneApiButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TestPhoneApiButton.IsEnabled = false;
                StatusTextBlock.Text = "正在测试手机API连接...";

                var name = PhoneApiNameTextBox.Text.Trim();
                var apiKey = PhoneApiKeyTextBox.Text.Trim();
                var projectId = PhoneApiProjectIdTextBox.Text.Trim();
                var selectedCountry = GetSelectedCountry();

                if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(apiKey))
                {
                    MessageBox.Show("请填写用户名和API密钥", "配置错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (selectedCountry == null)
                {
                    MessageBox.Show("请选择国家/地区", "配置错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 创建临时PhoneApiService进行测试
                var phoneConfig = _configService.PhoneApi;
                using var testService = new PhoneApiService(
                    phoneConfig.BaseUrl,
                    name,
                    apiKey,
                    projectId,
                    selectedCountry.Code
                );

                // 测试获取用户信息
                var result = await testService.GetUserInfoAsync();

                if (result.Code == 200)
                {
                    StatusTextBlock.Text = "手机API测试成功！";
                    MessageBox.Show("手机API连接测试成功！\n\n用户信息获取正常，可以正常使用自动获取手机号码功能。", "测试成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusTextBlock.Text = $"手机API测试失败: {result.Message}";
                    MessageBox.Show($"手机API测试失败！\n\n错误信息: {result.Message}\n\n请检查用户名和API密钥是否正确。", "测试失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "手机API测试异常";
                MessageBox.Show($"手机API测试异常！\n\n错误详情: {ex.Message}", "测试异常", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                TestPhoneApiButton.IsEnabled = true;
            }
        }

        private void SavePhoneApiButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var name = PhoneApiNameTextBox.Text.Trim();
                var apiKey = PhoneApiKeyTextBox.Text.Trim();
                var projectId = PhoneApiProjectIdTextBox.Text.Trim();
                var autoMode = PhoneDurianModeRadio?.IsChecked == true || PhoneQianchuanModeRadio?.IsChecked == true || PhoneUsaModeRadio?.IsChecked == true;
                var selectedCountry = GetSelectedCountry();
                var isDurianMode = PhoneDurianModeRadio?.IsChecked == true;
                var isQianchuanMode = PhoneQianchuanModeRadio?.IsChecked == true;
                var isUsaMode = PhoneUsaModeRadio?.IsChecked == true;

                // 只有榴莲模式需要验证用户名和API密钥
                if (isDurianMode && (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(apiKey)))
                {
                    MessageBox.Show("榴莲模式时，用户名和API密钥不能为空", "配置错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 只有榴莲模式需要验证国家/地区选择
                if (isDurianMode && selectedCountry == null)
                {
                    MessageBox.Show("榴莲模式时，必须选择国家/地区", "配置错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var countryCode = selectedCountry?.Code ?? "th";

                // 保存配置（只保存基本配置，不影响Provider设置）
                _configService.UpdatePhoneApiConfig(name, apiKey, projectId, countryCode, autoMode);

                // 不同模式的提示信息
                if (isQianchuanMode)
                {
                    StatusTextBlock.Text = "千川模式配置已保存（使用config.json中的配置）";
                    MessageBox.Show("千川模式配置保存成功！\n注意：千川模式使用config.json中的token配置，无需填写用户名和密钥。", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else if (isUsaMode)
                {
                    StatusTextBlock.Text = "美国模式配置已保存（使用内置API配置）";
                    MessageBox.Show("美国模式配置保存成功！\n注意：美国模式使用内置API配置，无需填写用户名和密钥。", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusTextBlock.Text = "手机API配置已保存";
                    MessageBox.Show("手机API配置保存成功！", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                // 刷新AutomationService中的PhoneApiService
                _automationService.RefreshPhoneApiService();
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "保存配置失败";
                MessageBox.Show($"保存配置失败: {ex.Message}", "保存失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CopyRegistrationInfoToClipboard(RegistrationData data)
        {
            try
            {
                if (data == null)
                {
                    StatusTextBlock.Text = "无法复制注册信息：数据为空";
                    return;
                }

                // 构建复制内容：邮箱 邮箱密码 登录密码（用空格分隔）
                var emailPassword = string.IsNullOrEmpty(data.EmailPassword) ? "未设置" : data.EmailPassword;
                var copyContent = $"①邮箱账号：{data.Email} ②邮箱密码：{emailPassword} ③AWS密码：{data.Password} ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：{data.MfaInfo}";

                // 复制到剪贴板
                Clipboard.SetText(copyContent);
                StatusTextBlock.Text = $"注册信息已复制到剪贴板：{copyContent}";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"复制注册信息失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 设置配置相关控件的启用/禁用状态
        /// 在注册进行期间禁用配置修改，防止配置冲突
        /// </summary>
        /// <param name="enabled">true=启用配置修改，false=禁用配置修改</param>
        private void SetConfigurationControlsEnabled(bool enabled)
        {
            try
            {
                // 浏览器模式选择 - 使用安全的控件访问
                try { if (AdsPowerModeRadio != null) AdsPowerModeRadio.IsEnabled = enabled; } catch { }
                try { if (LocalChromeIncognitoModeRadio != null) LocalChromeIncognitoModeRadio.IsEnabled = enabled; } catch { }
                try { if (LocalChromeNormalModeRadio != null) LocalChromeNormalModeRadio.IsEnabled = enabled; } catch { }

                // 手机号码模式选择
                try { if (PhoneDurianModeRadio != null) PhoneDurianModeRadio.IsEnabled = enabled; } catch { }
                try { if (PhoneQianchuanModeRadio != null) PhoneQianchuanModeRadio.IsEnabled = enabled; } catch { }
                try { if (PhoneUsaModeRadio != null) PhoneUsaModeRadio.IsEnabled = enabled; } catch { }
                try { if (PhoneManualModeRadio != null) PhoneManualModeRadio.IsEnabled = enabled; } catch { }

                // 手机API配置
                try { if (PhoneApiNameTextBox != null) PhoneApiNameTextBox.IsEnabled = enabled; } catch { }
                try { if (PhoneApiKeyTextBox != null) PhoneApiKeyTextBox.IsEnabled = enabled; } catch { }
                try { if (PhoneApiProjectIdTextBox != null) PhoneApiProjectIdTextBox.IsEnabled = enabled; } catch { }
                try { if (CountrySearchTextBox != null) CountrySearchTextBox.IsEnabled = enabled; } catch { }
                try { if (TestPhoneApiButton != null) TestPhoneApiButton.IsEnabled = enabled; } catch { }
                try { if (SavePhoneApiButton != null) SavePhoneApiButton.IsEnabled = enabled; } catch { }

                // 邮箱验证码模式选择
                try { if (EmailMicrosoftRadio != null) EmailMicrosoftRadio.IsEnabled = enabled; } catch { }
                try { if (EmailGoogleRadio != null) EmailGoogleRadio.IsEnabled = enabled; } catch { }
                try { if (EmailManualModeRadio != null) EmailManualModeRadio.IsEnabled = enabled; } catch { }

                // 图形验证码模式选择
                try { if (CaptchaYesModeRadio != null) CaptchaYesModeRadio.IsEnabled = enabled; } catch { }
                try { if (CaptchaCloudModeRadio != null) CaptchaCloudModeRadio.IsEnabled = enabled; } catch { }
                try { if (CaptchaManualModeRadio != null) CaptchaManualModeRadio.IsEnabled = enabled; } catch { }

                // 线程数量选择
                try { if (ThreadCountComboBox != null) ThreadCountComboBox.IsEnabled = enabled; } catch { }

                // AdsPower配置
                try { if (ProfileIdTextBox != null) ProfileIdTextBox.IsEnabled = enabled; } catch { }
                try { if (TestConnectionButton != null) TestConnectionButton.IsEnabled = enabled; } catch { }

                // 本地Chrome配置
                try { if (TestLocalChromeButton != null) TestLocalChromeButton.IsEnabled = enabled; } catch { }

                // 更新状态提示
                try
                {
                    if (!enabled && StatusTextBlock != null)
                    {
                        var currentStatus = StatusTextBlock.Text;
                        if (!currentStatus.Contains("配置已锁定"))
                        {
                            StatusTextBlock.Text = currentStatus + " (配置已锁定，注册完成后自动解锁)";
                        }
                    }
                    else if (enabled && StatusTextBlock != null)
                    {
                        // 恢复时移除锁定提示
                        var currentStatus = StatusTextBlock.Text;
                        if (currentStatus.Contains(" (配置已锁定，注册完成后自动解锁)"))
                        {
                            StatusTextBlock.Text = currentStatus.Replace(" (配置已锁定，注册完成后自动解锁)", "");
                        }
                    }
                }
                catch { }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SetConfigurationControlsEnabled error: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理单个线程终止后的数据移动
        /// </summary>
        public void HandleSingleThreadTerminatedData(int threadId, List<RegistrationData> terminatedDataList)
        {
            try
            {
                _logService.LogInfo($"开始处理线程{threadId}终止数据，共{terminatedDataList.Count}个数据");

                int movedCount = 0;
                foreach (var dataToTerminate in terminatedDataList)
                {
                    _logService.LogInfo($"处理线程{threadId}终止数据: {dataToTerminate.Email}");

                    // 从注册数据列表中移除（如果存在）
                    var existingData = _registrationDataList.FirstOrDefault(d => d.Email == dataToTerminate.Email);
                    if (existingData != null)
                    {
                        // 更新数据状态为已终止
                        UpdateDataStatus(existingData, Models.DataProcessStatus.Failed, $"线程{threadId}手动终止");

                        _registrationDataList.Remove(existingData);
                        _logService.LogInfo($"从注册数据列表中移除: {dataToTerminate.Email}");
                    }

                    // 移动到已被终止注册数据区域
                    _fileService.MoveTerminatedData(_selectedFilePath, dataToTerminate);
                    movedCount++;

                    _logService.LogInfo($"线程{threadId}终止 - 数据已移动到终止列表: {dataToTerminate.Email}");
                }

                // 强制刷新界面显示
                Dispatcher.Invoke(() =>
                {
                    DataGrid.Items.Refresh();
                    UpdateDataStatistics();
                });

                // 更新状态显示
                StatusTextBlock.Text = $"线程{threadId}终止完成，{movedCount} 个数据已移动到终止列表";
                _logService.LogInfo($"线程{threadId}终止数据处理完成，成功移动{movedCount}个数据");
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理线程{threadId}终止数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理多线程批量终止后的数据移动
        /// </summary>
        public void HandleTerminatedDataFromMultiThread(List<RegistrationData> terminatedDataList)
        {
            try
            {
                _logService.LogInfo($"开始处理多线程终止数据，共{terminatedDataList.Count}个数据");
                _logService.LogInfo($"当前注册数据列表中有{_registrationDataList.Count}条数据");

                int movedCount = 0;
                foreach (var dataToTerminate in terminatedDataList)
                {
                    _logService.LogInfo($"处理终止数据: {dataToTerminate.Email}");
                    _logService.LogInfo($"数据详情: {dataToTerminate.ToLine()}");

                    // 从注册数据列表中移除（如果存在）
                    var existingData = _registrationDataList.FirstOrDefault(d => d.Email == dataToTerminate.Email);
                    if (existingData != null)
                    {
                        // 更新数据状态为已终止
                        UpdateDataStatus(existingData, Models.DataProcessStatus.Failed, "批量终止");

                        _registrationDataList.Remove(existingData);
                        _logService.LogInfo($"从注册数据列表中移除: {dataToTerminate.Email}");
                    }
                    else
                    {
                        _logService.LogWarning($"注册数据列表中未找到: {dataToTerminate.Email}");
                    }

                    // 移动到已被终止注册数据区域
                    _fileService.MoveTerminatedData(_selectedFilePath, dataToTerminate);
                    movedCount++;

                    _logService.LogInfo($"多线程终止 - 数据已移动到终止列表: {dataToTerminate.Email}");
                }

                // 强制刷新界面显示
                Dispatcher.Invoke(() =>
                {
                    DataGrid.Items.Refresh();
                    UpdateDataStatistics();
                });

                // 更新状态显示
                StatusTextBlock.Text = $"多线程终止完成，{movedCount} 个数据已移动到终止列表";
                _logService.LogInfo($"多线程终止数据处理完成，成功移动{movedCount}个数据");
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理多线程终止数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 多线程终止后恢复主窗口按钮状态
        /// </summary>
        public void RestoreButtonStateAfterMultiThreadTermination()
        {
            try
            {
                // 恢复按钮状态，类似于单线程终止注册后的状态
                StartButton.IsEnabled = true;
                StopButton.IsEnabled = false;
                PauseButton.IsEnabled = false;
                ContinueButton.IsEnabled = false;

                // 更新状态
                StatusTextBlock.Text = "多线程注册已全部终止，可以开始新的注册";
                _logService.LogInfo("多线程终止后主窗口按钮状态已恢复");
            }
            catch (Exception ex)
            {
                _logService.LogError($"恢复主窗口按钮状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 程序退出时的清理工作
        /// </summary>
        private async void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                _logService.LogInfo("程序正在退出，开始清理工作...");

                // 清理千川黑名单队列
                await QianchuanPhoneNumberBlacklistManager.CleanupOnExit();

                // 清理榴莲释放队列
                await PhoneNumberReleaseManager.CleanupOnExit();

                _logService.LogInfo("程序退出清理工作完成");
            }
            catch (Exception ex)
            {
                _logService.LogError($"程序退出清理失败: {ex.Message}");
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _automationService?.Dispose();
            _adsService?.Dispose();
            base.OnClosed(e);
        }
    }
}
