Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AWSAutoRegister", "AWSAutoRegister.csproj", "{D2865864-4C98-D749-37C4-EF19A17EFE61}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D2865864-4C98-D749-37C4-EF19A17EFE61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2865864-4C98-D749-37C4-EF19A17EFE61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2865864-4C98-D749-37C4-EF19A17EFE61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2865864-4C98-D749-37C4-EF19A17EFE61}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {035F8C63-4C5F-4E26-92CA-33DF483F5880}
	EndGlobalSection
EndGlobal
