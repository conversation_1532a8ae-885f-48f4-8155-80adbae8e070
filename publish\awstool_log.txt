2025-08-01 13:43:29 [信息] AWS自动注册工具启动
2025-08-01 13:43:29 [信息] 程序版本: 1.0.0.0
2025-08-01 13:43:29 [信息] 启动时间: 2025-08-01 13:43:29
2025-08-01 13:43:29 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 13:43:29 [信息] 线程数量已选择: 1
2025-08-01 13:43:29 [信息] 线程数量选择初始化完成
2025-08-01 13:43:29 [信息] 程序初始化完成
2025-08-01 13:43:32 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 13:43:34 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-01-印度尼西亚.txt
2025-08-01 13:43:35 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-01-印度尼西亚.txt
2025-08-01 13:43:35 [信息] 成功加载 4 条数据
2025-08-01 13:43:36 [信息] 线程数量已选择: 2
2025-08-01 13:52:09 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 13:52:09 [信息] 开始启动多线程注册，线程数量: 2
2025-08-01 13:52:09 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 4
2025-08-01 13:52:09 [信息] 所有线程已停止并清理
2025-08-01 13:52:09 [信息] 正在初始化多线程服务...
2025-08-01 13:52:09 [信息] 榴莲手机API服务已初始化
2025-08-01 13:52:09 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-01 13:52:09 [信息] 多线程服务初始化完成
2025-08-01 13:52:09 [信息] 数据分配完成：共4条数据分配给2个线程
2025-08-01 13:52:09 [信息] 线程1分配到2条数据
2025-08-01 13:52:09 [信息] 线程2分配到2条数据
2025-08-01 13:52:09 [信息] 屏幕工作区域: 1280x672
2025-08-01 13:52:09 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 13:52:09 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 13:52:09 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 13:52:09 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 13:52:09 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 13:52:09 [信息] 屏幕工作区域: 1280x672
2025-08-01 13:52:09 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 13:52:09 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-01 13:52:09 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 13:52:09 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 13:52:09 [信息] 线程2已创建，窗口位置: (0, 329)
2025-08-01 13:52:09 [信息] 多线程注册启动成功，共2个线程
2025-08-01 13:52:09 线程1：[信息] 开始启动注册流程
2025-08-01 13:52:09 线程2：[信息] 开始启动注册流程
2025-08-01 13:52:09 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 13:52:09 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 13:52:09 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 13:52:09 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 13:52:09 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 13:52:09 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 13:52:09 [信息] 多线程管理窗口已初始化
2025-08-01 13:52:09 [信息] UniformGrid列数已更新为: 1
2025-08-01 13:52:09 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 13:52:10 [信息] 多线程管理窗口已打开
2025-08-01 13:52:10 [信息] 多线程注册启动成功，共2个线程
2025-08-01 13:52:11 [信息] UniformGrid列数已更新为: 1
2025-08-01 13:52:11 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 13:52:11 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 13:52:11 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 13:52:11 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-01 13:52:11 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 13:52:11 [信息] UniformGrid列数已更新为: 1
2025-08-01 13:52:11 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 13:52:11 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 13:52:11 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 13:52:11 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 13:52:11 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 13:52:12 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 13:52:13 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 13:52:16 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 13:52:18 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 13:52:18 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 13:52:18 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 13:52:18 线程2：[信息] 浏览器启动成功
2025-08-01 13:52:18 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 13:52:18 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 13:52:18 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 13:52:18 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 13:52:18 线程1：[信息] 浏览器启动成功
2025-08-01 13:52:18 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 13:52:18 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 13:52:18 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 13:52:18 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 13:52:18 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 13:52:34 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 13:52:34 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 13:52:34 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 13:52:35 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 13:52:35 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 13:52:35 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 13:52:38 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 13:52:38 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 13:52:38 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 13:52:38 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 13:52:38 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:52:38 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 13:52:40 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 13:52:40 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 13:52:40 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 13:52:40 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-01 13:52:41 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 13:52:41 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 13:52:41 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 13:52:41 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 13:52:41 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 13:52:41 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 13:52:42 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 13:52:42 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 13:52:42
2025-08-01 13:52:44 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 13:52:44 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-01 13:52:44 [信息] 检测到错误信息，开始重试机制
2025-08-01 13:52:44 线程1：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-01 13:52:44 [信息] 第1次重试点击验证邮箱按钮
2025-08-01 13:52:45 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 13:52:45 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 13:52:45
2025-08-01 13:52:46 线程1：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-01 13:52:46 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-01 13:52:48 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 13:52:48 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 13:52:48
2025-08-01 13:52:48 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-01 13:52:48 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-01 13:52:49 线程1：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-01 13:52:49 [信息] 第1次重试成功：已到达第二页
2025-08-01 13:52:49 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 13:52:49 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 13:52:49 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:52:49 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 13:52:51 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 13:52:51 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 13:52:51 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 13:52:51 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 13:52:51 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 13:52:51 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-08-01 13:52:51 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 13:52:51
2025-08-01 13:52:51 [信息] [线程2] 邮箱验证码获取成功: 039539，立即停止重复请求
2025-08-01 13:52:51 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-01 13:52:51 [信息] [线程2] 已清理响应文件
2025-08-01 13:52:51 线程2：[信息] [信息] 验证码获取成功: 039539，正在自动填入... (进度: 25%)
2025-08-01 13:52:51 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 13:52:51 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 13:52:51 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 13:52:51 [信息] 线程2完成第二页事件已处理
2025-08-01 13:52:51 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-01 13:52:51 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 13:52:51 [信息] 开始批量获取2个手机号码，服务商: Durian
2025-08-01 13:52:51 [信息] [手机API] 批量获取2个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=2&noblack=0&serial=2&secret_key=null&vip=null
2025-08-01 13:52:53 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 13:52:53 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 13:52:53
2025-08-01 13:52:54 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+525512803625","+523521072903"]}
2025-08-01 13:52:54 [信息] [手机API] 检测到数组格式，元素数量: 2
2025-08-01 13:52:54 [信息] [手机API] 批量获取成功，获得2个手机号码
2025-08-01 13:52:54 [信息] 线程1分配榴莲手机号码: +525512803625
2025-08-01 13:52:54 [信息] 线程2分配榴莲手机号码: +523521072903
2025-08-01 13:52:54 [信息] 榴莲API批量获取手机号码成功，已分配给2个线程
2025-08-01 13:52:54 [信息] 批量获取2个手机号码成功
2025-08-01 13:52:54 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 13:52:54 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 13:52:55 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:52:55 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:52:55 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 13:52:56 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 13:52:56 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 13:52:56
2025-08-01 13:52:56 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 13:52:58 [信息] [线程1] 邮箱验证码获取成功: 513675，立即停止重复请求
2025-08-01 13:52:58 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-01 13:52:58 [信息] [线程1] 已清理响应文件
2025-08-01 13:52:58 线程1：[信息] [信息] 验证码获取成功: 513675，正在自动填入... (进度: 25%)
2025-08-01 13:52:58 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 13:52:58 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 13:52:58 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 13:52:58 [信息] 线程1完成第二页事件已处理
2025-08-01 13:52:58 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-01 13:52:58 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 13:52:59 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 13:52:59 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 13:52:59 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 13:53:01 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 13:53:01 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 13:53:02 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:53:02 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:53:02 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 13:53:03 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 13:53:03 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 13:53:03 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 13:53:06 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 13:53:06 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 13:53:06 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 13:53:11 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 13:53:11 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 13:53:12 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 13:53:12 [信息] 线程2获取已分配的榴莲手机号码: +523521072903
2025-08-01 13:53:12 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +523521072903 (进度: 38%)
2025-08-01 13:53:13 线程2：[信息] [信息] 数据国家代码为ID，需要选择Indonesia (进度: 38%)
2025-08-01 13:53:13 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 13:53:16 线程2：[信息] [信息] 已选择国家: Indonesia (进度: 38%)
2025-08-01 13:53:16 线程2：[信息] [信息] 已成功选择国家: Indonesia (进度: 38%)
2025-08-01 13:53:16 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-01 13:53:16 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 13:53:19 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-01 13:53:19 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-01 13:53:19 线程2：[信息] [信息] 已自动获取并填入手机号码: +523521072903 (进度: 38%)
2025-08-01 13:53:20 线程2：[信息] [信息] 使用已获取的手机号码: +523521072903（保存本地号码: +523521072903） (进度: 38%)
2025-08-01 13:53:20 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-01 13:53:24 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-01 13:53:25 线程2：[信息] [信息] 正在选择月份: December (进度: 38%)
2025-08-01 13:53:25 线程2：[信息] [信息] 已选择月份（标准选项）: December (进度: 38%)
2025-08-01 13:53:26 线程2：[信息] [信息] 正在选择年份: 2030 (进度: 38%)
2025-08-01 13:53:26 线程2：[信息] [信息] 已选择年份（标准选项）: 2030 (进度: 38%)
2025-08-01 13:53:27 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 13:53:27 [信息] 线程1获取已分配的榴莲手机号码: +525512803625
2025-08-01 13:53:27 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +525512803625 (进度: 38%)
2025-08-01 13:53:28 线程1：[信息] [信息] 数据国家代码为ID，需要选择Indonesia (进度: 38%)
2025-08-01 13:53:28 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 13:53:29 线程1：[信息] [信息] 已选择国家: Indonesia (进度: 38%)
2025-08-01 13:53:29 线程1：[信息] [信息] 已成功选择国家: Indonesia (进度: 38%)
2025-08-01 13:53:29 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-01 13:53:30 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 13:53:32 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-01 13:53:33 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-01 13:53:33 线程1：[信息] [信息] 已自动获取并填入手机号码: +525512803625 (进度: 38%)
2025-08-01 13:53:34 线程1：[信息] [信息] 使用已获取的手机号码: +525512803625（保存本地号码: +525512803625） (进度: 38%)
2025-08-01 13:53:34 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-01 13:53:36 线程2：[信息] [信息] 所有自动线程已停止 (进度: 38%)
2025-08-01 13:53:36 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 38%)
2025-08-01 13:53:36 线程2：[信息] 已暂停
2025-08-01 13:53:36 [信息] 线程2已暂停
2025-08-01 13:53:36 [信息] 线程2已暂停
2025-08-01 13:53:37 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-01 13:53:38 线程1：[信息] [信息] 正在选择月份: April (进度: 38%)
2025-08-01 13:53:38 线程1：[信息] [信息] 已选择月份（标准选项）: April (进度: 38%)
2025-08-01 13:53:39 线程1：[信息] [信息] 正在选择年份: 2026 (进度: 38%)
2025-08-01 13:53:39 线程1：[信息] [信息] 已选择年份（标准选项）: 2026 (进度: 38%)
2025-08-01 13:53:49 线程1：[信息] [信息] 所有自动线程已停止 (进度: 38%)
2025-08-01 13:53:49 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 38%)
2025-08-01 13:53:49 线程1：[信息] 已暂停
2025-08-01 13:53:49 [信息] 线程1已暂停
2025-08-01 13:53:49 [信息] 线程1已暂停
2025-08-01 13:53:56 线程2：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Security code" })
  -   locator resolved to <input value="" disabled id="sor.cvv" name="sor.cvv" ty…/>
  - elementHandle.fill("")
  -   waiting for element to be visible, enabled and editable
  -     element is not enabled - waiting...，但手机号码已保存 (进度: 58%)
2025-08-01 13:54:09 线程1：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Security code" })
  -   locator resolved to <input value="" disabled id="sor.cvv" name="sor.cvv" ty…/>
  - elementHandle.fill("")
  -   waiting for element to be visible, enabled and editable
  -     element is not enabled - waiting...，但手机号码已保存 (进度: 58%)
2025-08-01 14:47:45 [信息] 多线程窗口引用已清理
2025-08-01 14:47:45 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 14:47:45 [信息] 多线程管理窗口正在关闭
2025-08-01 14:47:45 [信息] 程序正在退出，开始清理工作...
2025-08-01 14:47:45 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 14:47:45 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 14:47:45 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 14:47:45 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 14:47:45 [信息] 程序退出清理工作完成
