using System;
using System.IO;
using System.Threading.Tasks;
using AWSAutoRegister.Models;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 注册线程实例 - 封装单个注册线程的完整生命周期管理
    /// 完全复制现有AutomationService，不修改任何现有逻辑
    /// </summary>
    public class RegistrationThread
    {
        private readonly int _threadId;
        private readonly AutomationService _automationService;
        private readonly Queue<RegistrationData> _dataQueue = new Queue<RegistrationData>();
        private readonly WindowPosition _windowPosition;
        private readonly BrowserFingerprint _fingerprint;
        private readonly LogService _logService;
        private ThreadStatusInfo _status;
        private string _tempUserDataDir = string.Empty;
        private CancellationTokenSource? _cancellationTokenSource;
        private string? _currentEmail = null; // 当前处理的邮箱
        private RegistrationData? _currentData = null; // 当前处理的完整数据
        private string _completionType = "WithoutKeys"; // 完成类型，默认为无密钥

        // 事件
        public event Action<int, ThreadStatusInfo>? StatusChanged;
        public event Action<int, string>? ManualActionRequired;
        public event Action<int, RegistrationData>? DataCompleted;
        public event Action<int, RegistrationData>? DataStarted; // 数据开始处理事件
        public event Action<int, string>? SaveClipboardInfo; // 保存剪贴板信息事件
        public event Action<int, string, RegistrationData?>? SaveFailedData; // 保存失败数据事件
        public event Action<int>? Page2Completed; // 第二页完成事件

        public int ThreadId => _threadId;
        public ThreadStatusInfo Status => _status;

        private readonly MultiThreadPhoneNumberManager? _phoneManager;

        public RegistrationThread(int threadId, ConfigService configService, WindowPosition windowPosition, BrowserFingerprint fingerprint, MultiThreadPhoneNumberManager? phoneManager = null)
        {
            _threadId = threadId;
            _windowPosition = windowPosition;
            _fingerprint = fingerprint;
            _phoneManager = phoneManager;
            _logService = LogService.Instance;

            // 初始化状态
            _status = new ThreadStatusInfo
            {
                Status = ThreadStatus.NotStarted,
                Message = "线程已创建",
                Progress = 0,
                CurrentOperation = "等待启动"
            };

            // 创建AutomationService实例并注入所有服务
            _automationService = CreateThreadSpecificAutomationService(configService);

            _logService.LogInfo($"已创建，窗口位置: ({_windowPosition.X}, {_windowPosition.Y})", _threadId);
        }

        /// <summary>
        /// 创建线程特定的AutomationService实例
        /// </summary>
        private AutomationService CreateThreadSpecificAutomationService(ConfigService configService)
        {
            AutomationService service;

            if (_phoneManager != null)
            {
                // 多线程模式：使用带参构造函数
                service = new AutomationService(_threadId, configService, _phoneManager);
                _logService.LogInfo($"线程{_threadId}使用多线程模式创建AutomationService");
            }
            else
            {
                // 单线程模式：使用无参构造函数
                service = new AutomationService();
                // 设置线程ID（用于弹窗处理判断）
                service.SetThreadId(_threadId);
                // 注入共享的配置服务（所有线程使用相同配置）
                service.SetConfigService(configService);
                _logService.LogInfo($"线程{_threadId}使用单线程模式创建AutomationService");
            }

            // 设置窗口位置（多线程模式）
            service.SetWindowPosition(_windowPosition);

            // 订阅AutomationService事件并转发
            service.StatusChanged += OnAutomationServiceStatusChanged;
            service.RegistrationStatusChanged += OnAutomationServiceRegistrationStatusChanged;
            service.ShowInputDialog += OnAutomationServiceShowInputDialog;
            service.DataCompleted += OnAutomationServiceDataCompleted;
            service.DataCompletedWithoutKeys += OnAutomationServiceDataCompletedWithoutKeys;
            service.DataCompletedWithBillingIssue += OnAutomationServiceDataCompletedWithBillingIssue;
            service.DataCompletedWithIneligibleIssue += OnAutomationServiceDataCompletedWithIneligibleIssue;
            service.SaveClipboardInfoToSuccessData += OnAutomationServiceSaveClipboardInfo;
            service.SaveFailedData += OnAutomationServiceSaveFailedData;
            service.Page2Completed += OnAutomationServicePage2Completed;

            return service;
        }

        /// <summary>
        /// 启动注册流程
        /// </summary>
        public async Task StartRegistrationAsync()
        {
            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                
                UpdateStatus(ThreadStatus.Initializing, "正在初始化线程...", 0, "初始化浏览器");
                _logService.LogInfo($"开始启动注册流程", _threadId);

                // 创建临时用户数据目录
                _tempUserDataDir = Path.Combine(Path.GetTempPath(), $"AWS_Thread_{_threadId}_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(_tempUserDataDir);

                // 设置浏览器窗口位置和指纹
                await SetupBrowserEnvironment();

                UpdateStatus(ThreadStatus.Processing, "线程已启动，等待数据...", 10, "等待数据分配");

                // 开始处理数据队列
                await ProcessDataQueue();
            }
            catch (OperationCanceledException)
            {
                _logService.LogInfo($"已被取消", _threadId);
                UpdateStatus(ThreadStatus.Terminated, "线程已被取消", 0, "已取消");
            }
            catch (Exception ex)
            {
                _logService.LogError($"启动失败: {ex.Message}", _threadId);
                UpdateStatus(ThreadStatus.Failed, $"启动失败: {ex.Message}", 0, "启动失败");
            }
        }

        /// <summary>
        /// 暂停线程
        /// </summary>
        public async Task PauseAsync()
        {
            try
            {
                await _automationService.PauseRegistrationAsync();
                UpdateStatus(ThreadStatus.Paused, "线程已暂停", _status.Progress, "已暂停");
                _logService.LogInfo($"已暂停", _threadId);
            }
            catch (Exception ex)
            {
                _logService.LogError($"暂停失败: {ex.Message}", _threadId);
            }
        }

        /// <summary>
        /// 继续线程
        /// </summary>
        public async Task ResumeAsync()
        {
            try
            {
                await _automationService.ResumeRegistrationAsync();
                // 不要显示"线程已继续"，让AutomationService自己更新具体状态
                _logService.LogInfo($"已继续", _threadId);
            }
            catch (Exception ex)
            {
                _logService.LogError($"继续失败: {ex.Message}", _threadId);
            }
        }

        /// <summary>
        /// 终止线程
        /// </summary>
        public async Task TerminateAsync()
        {
            try
            {
                // 记录终止时的当前数据
                if (_currentData != null)
                {
                    _logService.LogInfo($"终止时正在处理的数据: {_currentData.Email}", _threadId);
                    _logService.LogInfo($"数据详情: {_currentData.ToLine()}", _threadId);
                }
                else
                {
                    _logService.LogInfo($"终止时没有正在处理的数据", _threadId);
                }

                _cancellationTokenSource?.Cancel();

                // 在终止前复制密钥信息（与单线程保持一致）
                if (_currentData != null)
                {
                    _logService.LogInfo($"多线程终止 - 复制密钥信息到剪贴板", _threadId);
                    await _automationService.CopyTerminatedRegistrationInfoAsync();
                }

                await _automationService.TerminateRegistrationAsync();

                // 清理临时目录
                CleanupTempDirectory();

                UpdateStatus(ThreadStatus.Terminated, "线程已终止", 0, "已终止");
                _logService.LogInfo($"已终止", _threadId);
            }
            catch (Exception ex)
            {
                _logService.LogError($"终止失败: {ex.Message}", _threadId);
            }
        }

        /// <summary>
        /// 获取线程状态
        /// </summary>
        public ThreadStatusInfo GetStatus()
        {
            return _status;
        }

        /// <summary>
        /// 获取当前处理的邮箱
        /// </summary>
        public string? GetCurrentEmail()
        {
            return _currentEmail;
        }

        /// <summary>
        /// 获取当前处理的完整数据
        /// </summary>
        public RegistrationData? GetCurrentData()
        {
            return _currentData;
        }

        /// <summary>
        /// 获取完成类型
        /// </summary>
        public string GetCompletionType()
        {
            return _completionType;
        }

        /// <summary>
        /// 添加数据到队列
        /// </summary>
        public void AddDataToQueue(RegistrationData data)
        {
            lock (_dataQueue)
            {
                _dataQueue.Enqueue(data);
                _logService.LogInfo($"添加数据到队列: {data.Email}", _threadId);
            }
        }

        /// <summary>
        /// 获取下一个数据
        /// </summary>
        private RegistrationData? GetNextData()
        {
            lock (_dataQueue)
            {
                if (_dataQueue.Count > 0)
                {
                    var data = _dataQueue.Dequeue();
                    _logService.LogInfo($"获取下一个数据: {data.Email}", _threadId);
                    return data;
                }
                return null;
            }
        }

        /// <summary>
        /// 设置浏览器环境
        /// </summary>
        private async Task SetupBrowserEnvironment()
        {
            try
            {
                _logService.LogInfo($"开始启动浏览器: 位置({_windowPosition.X}, {_windowPosition.Y}), UserAgent: {_fingerprint.UserAgent}", _threadId);

                // 根据配置服务的浏览器模式启动浏览器
                var configService = _automationService.GetConfigService();
                bool connected = false;

                if (configService?.BrowserMode == Services.BrowserMode.LocalChromeIncognito)
                {
                    _logService.LogInfo($"启动无痕Chrome浏览器...", _threadId);
                    connected = await _automationService.ConnectToLocalChromeAsync();
                }
                else
                {
                    _logService.LogInfo($"启动默认Chrome浏览器...", _threadId);
                    connected = await _automationService.ConnectToLocalChromeNormalAsync();
                }

                if (!connected)
                {
                    throw new Exception("浏览器启动失败，请确保系统已安装Google Chrome浏览器");
                }

                _logService.LogInfo($"浏览器启动成功", _threadId);
            }
            catch (Exception ex)
            {
                _logService.LogError($"浏览器启动失败: {ex.Message}", _threadId);
                throw;
            }
        }

        /// <summary>
        /// 处理分配的数据（每个线程只处理一个账户）
        /// </summary>
        private async Task ProcessDataQueue()
        {
            // 获取分配给这个线程的数据
            var data = GetNextData();
            if (data == null)
            {
                UpdateStatus(ThreadStatus.Failed, "没有分配到数据", 0, "无数据");
                _logService.LogError($"线程启动但没有分配到数据", _threadId);
                return;
            }

            try
            {
                // 设置当前处理的邮箱和完整数据
                _currentEmail = data.Email;
                _currentData = data;

                // 通知主界面数据开始被处理
                DataStarted?.Invoke(_threadId, data);

                UpdateStatus(ThreadStatus.Processing, $"正在处理: {data.Email}", 0, "准备开始注册");
                _logService.LogInfo($"开始处理账户: {data.Email}", _threadId);

                // 使用AutomationService处理数据（启动注册流程）
                var success = await _automationService.StartRegistrationAsync(data);

                if (success)
                {
                    // 注册流程已启动，但不代表完成
                    // 完成状态将通过AutomationService的事件回调来处理
                    UpdateStatus(ThreadStatus.Processing, $"注册流程已启动: {data.Email}", 5, "注册流程运行中");
                    _logService.LogInfo($"账户注册流程已启动: {data.Email}", _threadId);

                    // 注意：不在这里设置完成状态，而是等待AutomationService的事件回调
                    // 真正的完成状态将在OnAutomationServiceDataCompleted等方法中处理
                }
                else
                {
                    UpdateStatus(ThreadStatus.Failed, $"注册启动失败: {data.Email}", 0, "启动失败");
                    _logService.LogError($"账户注册启动失败: {data.Email}", _threadId);
                }
            }
            catch (OperationCanceledException)
            {
                _logService.LogInfo($"注册被取消: {data.Email}", _threadId);
                UpdateStatus(ThreadStatus.Terminated, "注册已取消", 0, "已取消");
            }
            catch (Exception ex)
            {
                _logService.LogError($"注册异常: {data.Email}, 错误: {ex.Message}", _threadId);
                UpdateStatus(ThreadStatus.Failed, $"注册异常: {ex.Message}", 0, "注册异常");
            }
            finally
            {
                // 注意：不在这里清空当前数据，因为注册流程可能还在进行中
                // 数据清空将在真正完成时进行
                _logService.LogInfo($"线程数据处理方法完成，注册流程可能仍在进行中", _threadId);
            }
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        private void UpdateStatus(ThreadStatus status, string message, int progress, string operation)
        {
            _status.Status = status;
            _status.Message = message;
            _status.Progress = progress;
            _status.CurrentOperation = operation;
            
            StatusChanged?.Invoke(_threadId, _status);
        }

        /// <summary>
        /// 清理临时目录
        /// </summary>
        private void CleanupTempDirectory()
        {
            try
            {
                if (!string.IsNullOrEmpty(_tempUserDataDir) && Directory.Exists(_tempUserDataDir))
                {
                    Directory.Delete(_tempUserDataDir, true);
                    _logService.LogInfo($"临时目录已清理: {_tempUserDataDir}", _threadId);
                }
            }
            catch (Exception ex)
            {
                _logService.LogWarning($"清理临时目录失败: {ex.Message}", _threadId);
            }
        }

        // AutomationService事件处理方法
        private void OnAutomationServiceStatusChanged(string status)
        {
            // 根据状态消息智能更新进度
            int progress = CalculateProgressFromStatus(status);
            string operation = ExtractOperationFromStatus(status);

            // 更新UI状态
            UpdateStatus(_status.Status, status, progress, operation);

            // 记录详细的操作日志到文件
            _logService.LogInfo($"[信息] {status} (进度: {progress}%)", _threadId);
        }

        /// <summary>
        /// 根据状态消息智能计算进度 - 优化版本，避免进度跳跃
        /// </summary>
        private int CalculateProgressFromStatus(string status)
        {
            if (string.IsNullOrEmpty(status)) return _status.Progress;

            // AWS注册流程总共分为10个主要阶段，每个阶段约10%
            // 阶段0: 准备和启动 (0-5%)
            // 阶段1: 初始化和第一页 (5-15%)
            // 阶段2: 邮箱验证码 (15-25%)
            // 阶段3: 第二页验证 (25-35%)
            // 阶段4: 第三页密码设置 (35-45%)
            // 阶段5: 第四页地址信息 (45-55%)
            // 阶段6: 第五页支付信息 (55-65%)
            // 阶段7: 第六页手机验证 (65-75%)
            // 阶段8: 第七页SMS验证 (75-85%)
            // 阶段9: 完成注册和跳转 (85-95%)
            // 阶段10: 密钥提取 (95-100%)

            int newProgress = _status.Progress; // 默认保持当前进度

            // 阶段0: 准备和启动 (0-5%)
            if (status.Contains("准备开始注册") || status.Contains("正在启动浏览器"))
                newProgress = 2;
            else if (status.Contains("浏览器") && status.Contains("连接"))
                newProgress = 5;

            // 阶段1: 初始化和第一页 (5-15%)
            else if (status.Contains("正在打开AWS注册页面"))
                newProgress = 8;
            else if (status.Contains("正在执行第一页") || status.Contains("开始处理第一页") || status.Contains("ExecuteStep1"))
                newProgress = 10;
            else if (status.Contains("第一页") && (status.Contains("基本信息") || status.Contains("填写完成") || status.Contains("完成")))
                newProgress = 15;

            // 阶段2: 邮箱验证码 (15-25%)
            else if (status.Contains("等待验证码页面") || status.Contains("邮箱验证码"))
                newProgress = 18;
            else if (status.Contains("验证码") && status.Contains("成功") && !status.Contains("手机"))
                newProgress = 25;

            // 阶段3: 第二页验证 (25-35%)
            else if (status.Contains("开始处理第二页") || status.Contains("ExecuteStep2") ||
                     (status.Contains("第二页") && !status.Contains("完成")))
                newProgress = 28;
            else if (status.Contains("第二页") && status.Contains("完成"))
                newProgress = 35;

            // 阶段4: 第三页密码设置 (35-45%)
            else if (status.Contains("开始处理第三页") || status.Contains("ExecuteStep3") ||
                     status.Contains("第三页") || status.Contains("密码设置"))
                newProgress = 38;
            else if (status.Contains("第三页") && status.Contains("完成"))
                newProgress = 45;

            // 阶段5: 第四页地址信息 (45-55%)
            else if (status.Contains("开始处理第四页") || status.Contains("ExecuteStep4") ||
                     status.Contains("第四页") || status.Contains("地址信息"))
                newProgress = 48;
            else if (status.Contains("第四页") && status.Contains("完成"))
                newProgress = 55;

            // 阶段6: 第五页支付信息 (55-65%)
            else if (status.Contains("开始处理第五页") || status.Contains("ExecuteStep5") ||
                     status.Contains("第五页") || status.Contains("支付信息"))
                newProgress = 58;
            else if (status.Contains("第五页") && status.Contains("完成"))
                newProgress = 65;

            // 阶段7: 第六页手机验证 (65-75%)
            else if (status.Contains("开始处理第六页") || status.Contains("ExecuteStep6") ||
                     status.Contains("第六页") || status.Contains("手机验证") || status.Contains("图形验证码"))
                newProgress = 68;
            else if (status.Contains("第六页") && status.Contains("完成"))
                newProgress = 75;

            // 阶段8: 第七页SMS验证 (75-85%)
            else if (status.Contains("开始处理第七页") || status.Contains("ExecuteStep7") ||
                     status.Contains("第七页") || status.Contains("SMS") || status.Contains("短信验证") ||
                     status.Contains("手机验证码"))
                newProgress = 78;
            else if (status.Contains("第七页") && status.Contains("完成"))
                newProgress = 85;

            // 阶段9: 完成注册和跳转 (85-95%)
            else if (status.Contains("完成注册") || status.Contains("跳转管理控制台") || status.Contains("Complete sign up"))
                newProgress = 88;
            else if (status.Contains("控制台") && status.Contains("成功"))
                newProgress = 95;

            // 阶段10: 密钥提取 (95-100%)
            else if (status.Contains("密钥") || status.Contains("访问密钥") || status.Contains("开始密钥提取"))
                newProgress = 98;
            else if (status.Contains("注册成功") || status.Contains("✅") || status.Contains("密钥提取完成"))
                newProgress = 100;

            // 特殊处理：等待手动操作时不增加进度
            if (status.Contains("等待手动") || status.Contains("请手动") || status.Contains("手动输入") ||
                status.Contains("WaitingFor") || status.Contains("暂停"))
            {
                return _status.Progress; // 保持当前进度，不增加
            }

            // 确保进度只能向前，不能倒退（除非重新开始）
            if (newProgress > _status.Progress || _status.Progress == 0)
            {
                return newProgress;
            }

            return _status.Progress; // 保持当前进度，避免倒退
        }

        /// <summary>
        /// 从状态消息中提取操作描述 - 优化版本，提供更精确的操作描述
        /// </summary>
        private string ExtractOperationFromStatus(string status)
        {
            if (string.IsNullOrEmpty(status)) return _status.CurrentOperation;

            // 根据状态消息提取精确的操作描述
            if (status.Contains("正在打开AWS注册页面")) return "打开注册页面";
            if (status.Contains("正在执行第一页")) return "填写基本信息";
            if (status.Contains("第一页") && status.Contains("图形验证码")) return "第一页验证码";
            if (status.Contains("第一页")) return "第一页基本信息";

            if (status.Contains("等待验证码页面")) return "等待邮箱验证";
            if (status.Contains("邮箱验证码")) return "邮箱验证码";
            if (status.Contains("验证码") && !status.Contains("图形")) return "邮箱验证";

            if (status.Contains("第二页")) return "第二页验证";
            if (status.Contains("第三页") || status.Contains("密码设置")) return "设置密码";
            if (status.Contains("第四页") || status.Contains("地址信息")) return "填写地址";
            if (status.Contains("第五页") || status.Contains("支付信息")) return "支付信息";

            if (status.Contains("第六页") && status.Contains("图形验证码")) return "第六页验证码";
            if (status.Contains("第六页") || status.Contains("手机验证")) return "手机验证";

            if (status.Contains("第七页") || status.Contains("SMS") || status.Contains("短信")) return "短信验证";

            if (status.Contains("完成注册")) return "完成注册";
            if (status.Contains("跳转管理控制台") || status.Contains("控制台")) return "跳转控制台";
            if (status.Contains("密钥") || status.Contains("访问密钥")) return "提取密钥";
            if (status.Contains("注册成功")) return "注册完成";

            if (status.Contains("等待手动")) return "等待手动操作";
            if (status.Contains("暂停")) return "已暂停";
            if (status.Contains("失败") || status.Contains("错误")) return "处理失败";

            return "处理中";
        }

        private void OnAutomationServiceRegistrationStatusChanged(RegistrationStatus status)
        {
            var threadStatus = status switch
            {
                RegistrationStatus.Running => ThreadStatus.Processing,
                RegistrationStatus.Paused => ThreadStatus.Paused,
                RegistrationStatus.Completed => ThreadStatus.Completed,
                RegistrationStatus.Error => ThreadStatus.Failed,
                RegistrationStatus.Terminated => ThreadStatus.Terminated,
                RegistrationStatus.WaitingForVerification => ThreadStatus.WaitingForManualAction,
                RegistrationStatus.WaitingForPhoneInput => ThreadStatus.WaitingForManualAction,
                RegistrationStatus.WaitingForSMSVerification => ThreadStatus.WaitingForManualAction,
                RegistrationStatus.WaitingForAddressCorrection => ThreadStatus.WaitingForManualAction,
                _ => ThreadStatus.Processing
            };

            UpdateStatus(threadStatus, _status.Message, _status.Progress, _status.CurrentOperation);
        }

        private void OnAutomationServiceShowInputDialog(string title, string message, bool isManualMode)
        {
            // 多线程模式：不弹窗，改为状态显示
            _status.NeedsUserAction = isManualMode;
            _status.Title = title;
            UpdateStatus(ThreadStatus.WaitingForManualAction, message, _status.Progress, title);
            
            if (isManualMode)
            {
                ManualActionRequired?.Invoke(_threadId, $"{title}: {message}");
            }
        }

        private void OnAutomationServiceDataCompleted(RegistrationData data)
        {
            // 设置为95%，表示即将完成，等待后续操作完成
            UpdateStatus(ThreadStatus.Processing, $"注册完成，正在处理后续操作: {data.Email}", 95, "注册完成，正在处理后续操作");
            _logService.LogInfo($"账户注册完成(包含密钥)，等待后续操作: {data.Email}", _threadId);

            // 检查IAM激活是否失败
            bool iamFailed = _automationService.IsIamActivationFailed();

            // 保存完成类型信息
            _completionType = iamFailed ? "WithKeysIamFailed" : "WithKeys";

            // 清空当前数据
            _currentEmail = null;
            _currentData = null;

            DataCompleted?.Invoke(_threadId, data);
        }

        private void OnAutomationServiceDataCompletedWithoutKeys(RegistrationData data)
        {
            // 设置为95%，表示即将完成，等待后续操作完成
            UpdateStatus(ThreadStatus.Processing, $"注册完成，正在处理后续操作: {data.Email}", 95, "注册完成，正在处理后续操作");
            _logService.LogInfo($"账户注册完成(无密钥)，等待后续操作: {data.Email}", _threadId);

            // 保存完成类型信息
            _completionType = "WithoutKeys";

            // 清空当前数据
            _currentEmail = null;
            _currentData = null;

            DataCompleted?.Invoke(_threadId, data);
        }

        private void OnAutomationServiceDataCompletedWithBillingIssue(RegistrationData data)
        {
            // 设置为95%，表示即将完成，等待后续操作完成
            UpdateStatus(ThreadStatus.Processing, $"注册完成，正在处理后续操作: {data.Email}", 95, "注册完成，正在处理后续操作");
            _logService.LogInfo($"账户注册完成(账单问题)，等待后续操作: {data.Email}", _threadId);

            // 保存完成类型信息
            _completionType = "WithBillingIssue";

            // 清空当前数据
            _currentEmail = null;
            _currentData = null;

            DataCompleted?.Invoke(_threadId, data);
        }

        private void OnAutomationServiceDataCompletedWithIneligibleIssue(RegistrationData data)
        {
            // 设置为95%，表示即将完成，等待后续操作完成
            UpdateStatus(ThreadStatus.Processing, $"注册完成，正在处理后续操作: {data.Email}", 95, "注册完成，正在处理后续操作");
            _logService.LogInfo($"账户注册完成(无资格问题)，等待后续操作: {data.Email}", _threadId);

            // 保存完成类型信息
            _completionType = "WithIneligibleIssue";

            // 清空当前数据
            _currentEmail = null;
            _currentData = null;

            DataCompleted?.Invoke(_threadId, data);
        }

        /// <summary>
        /// 最终完成注册（在所有后续操作完成后调用）
        /// </summary>
        public void FinalizeCompletion(RegistrationData data)
        {
            // 使用保存的完成类型
            var completionType = _completionType;

            var status = completionType switch
            {
                "WithKeys" => ThreadStatus.Completed,
                "WithKeysIamFailed" => ThreadStatus.Completed,
                "WithoutKeys" => ThreadStatus.CompletedWithIssues,
                "WithBillingIssue" => ThreadStatus.CompletedWithIssues,
                "WithIneligibleIssue" => ThreadStatus.CompletedWithIssues,
                _ => ThreadStatus.Completed
            };

            var message = completionType switch
            {
                "WithKeys" => $"注册完成，密钥提取成功: {data.Email}",
                "WithKeysIamFailed" => $"注册完成，密钥提取成功（IAM开启失败）: {data.Email}",
                "WithoutKeys" => $"注册完成，无法提取密钥: {data.Email}",
                "WithBillingIssue" => $"注册完成，账单提示无法提取密钥: {data.Email}",
                "WithIneligibleIssue" => $"注册完成，账户无新客户资格: {data.Email}",
                _ => $"注册完成: {data.Email}"
            };

            var operation = completionType switch
            {
                "WithKeys" => "注册完成，密钥提取成功",
                "WithKeysIamFailed" => "注册完成，密钥提取成功（IAM开启失败）",
                "WithoutKeys" => "注册完成，无法提取密钥",
                "WithBillingIssue" => "注册完成，账单提示无法提取密钥",
                "WithIneligibleIssue" => "注册完成，账户无新客户资格",
                _ => "注册完成"
            };

            UpdateStatus(status, message, 100, operation);
            _logService.LogInfo($"最终完成: {message} (类型: {completionType})", _threadId);
        }

        private void OnAutomationServiceSaveFailedData(string failureReason, RegistrationData? failedData)
        {
            _logService.LogInfo($"收到失败数据保存请求: {failureReason}, 数据: {failedData?.Email}", _threadId);

            // 根据失败原因显示具体的失败信息
            string displayMessage = failureReason switch
            {
                "验证手机出现区号" => "注册失败，验证手机出现区号",
                "支付信息错误" => "注册失败，支付信息错误",
                "卡号已被关联" => "注册失败，卡号已被关联",
                _ => $"注册失败: {failureReason}"
            };

            // 更新线程状态为失败，显示具体的失败原因
            UpdateStatus(ThreadStatus.Failed, displayMessage, 0, "注册失败");

            // 触发失败数据保存事件，传递给MultiThreadManager
            SaveFailedData?.Invoke(_threadId, failureReason, failedData);
        }

        private void OnAutomationServiceSaveClipboardInfo(string clipboardContent)
        {
            _logService.LogInfo($"收到剪贴板保存请求: {clipboardContent}", _threadId);
            SaveClipboardInfo?.Invoke(_threadId, clipboardContent);
        }

        private void OnAutomationServicePage2Completed(int threadId)
        {
            Page2Completed?.Invoke(threadId);
        }
    }
}
