using System;
using System.IO;
using System.Threading.Tasks;
using AWSAutoRegister.Models;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 多线程邮箱验证码服务 - 通过线程ID标识实现精确匹配
    /// </summary>
    public class MultiThreadEmailVerificationService
    {
        private const int TimeoutMinutes = 2; // 2分钟超时
        private const int InitialDelaySeconds = 2; // 初始延迟2秒
        private const int RetryIntervalSeconds = 3; // 3秒重试间隔
        private readonly LogService _logService;

        public MultiThreadEmailVerificationService()
        {
            _logService = LogService.Instance;
        }

        /// <summary>
        /// 获取邮箱验证码（多线程版本）
        /// </summary>
        public async Task<(bool Success, string? VerificationCode, string Message)> GetVerificationCodeAsync(string email, int threadId)
        {
            try
            {
                _logService.LogInfo($"[线程{threadId}] 开始邮箱验证码获取流程，邮箱: {email}");

                // 生成线程特定的文件名
                var requestFile = $"EmailCodeRequest_Thread{threadId}.txt";
                var responseFile = $"EmailCodeResponse_Thread{threadId}.txt";

                // 确保删除旧的请求和响应文件
                try
                {
                    if (File.Exists(requestFile))
                    {
                        File.Delete(requestFile);
                        _logService.LogInfo($"[线程{threadId}] 已删除旧的请求文件");
                    }
                    if (File.Exists(responseFile))
                    {
                        File.Delete(responseFile);
                        _logService.LogInfo($"[线程{threadId}] 已删除旧的响应文件");
                    }
                }
                catch (Exception ex)
                {
                    _logService.LogWarning($"[线程{threadId}] 删除旧文件失败: {ex.Message}");
                }

                // 第一次触发前等待2秒
                _logService.LogInfo($"[线程{threadId}] 等待{InitialDelaySeconds}秒后开始第一次触发...");
                await Task.Delay(InitialDelaySeconds * 1000);

                // 等待响应文件，2分钟超时
                var endTime = DateTime.Now.AddMinutes(TimeoutMinutes);
                var retryCount = 0;
                var lastTriggerTime = DateTime.Now.AddSeconds(-10); // 设置为足够早的时间，确保首次立即触发
                var maxRetryCount = 20; // 最多重试20次，防止无限循环
                var hasReceivedCode = false; // 添加成功状态标记

                while (DateTime.Now < endTime && retryCount < maxRetryCount && !hasReceivedCode)
                {
                    // 检查是否需要触发（每3秒触发一次）
                    if (DateTime.Now - lastTriggerTime >= TimeSpan.FromSeconds(RetryIntervalSeconds))
                    {
                        retryCount++;
                        lastTriggerTime = DateTime.Now;

                        // 检查是否超过最大重试次数
                        if (retryCount > maxRetryCount)
                        {
                            _logService.LogWarning($"[线程{threadId}] 已达到最大重试次数{maxRetryCount}，停止触发");
                            break;
                        }

                        _logService.LogInfo($"[线程{threadId}] 第{retryCount}次触发邮箱验证码获取...（最多{maxRetryCount}次）");

                        // 写入请求文件，包含线程ID标识
                        try
                        {
                            var requestContent = $"ThreadId:{threadId}|Email:{email}|Time:{DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                            await File.WriteAllTextAsync(requestFile, requestContent);
                            _logService.LogInfo($"[线程{threadId}] 已写入请求文件: {requestContent}");
                        }
                        catch (Exception ex)
                        {
                            _logService.LogError($"[线程{threadId}] 写入请求文件失败: {ex.Message}");
                        }
                    }

                    // 检查响应文件是否存在
                    if (File.Exists(responseFile))
                    {
                        try
                        {
                            var responseContent = await File.ReadAllTextAsync(responseFile);
                            //_logService.LogInfo($"[线程{threadId}] 检测到响应文件 {responseFile}，内容: {responseContent}");

                            // 解析响应内容
                            var result = ParseEmailResponse(responseContent, threadId);
                            if (result.Success)
                            {
                                _logService.LogInfo($"[线程{threadId}] 邮箱验证码获取成功: {result.VerificationCode}，立即停止重复请求");
                                hasReceivedCode = true; // 设置成功标记，停止循环

                                // 立即清理请求文件，防止继续触发
                                try
                                {
                                    if (File.Exists(requestFile))
                                    {
                                        File.Delete(requestFile);
                                        _logService.LogInfo($"[线程{threadId}] 已清理请求文件，停止重复触发");
                                    }
                                }
                                catch (Exception cleanupEx)
                                {
                                    _logService.LogWarning($"[线程{threadId}] 清理请求文件失败: {cleanupEx.Message}");
                                }

                                // 清理文件
                                CleanupFiles(requestFile, responseFile, threadId);
                                return result;
                            }
                            else
                            {
                                //_logService.LogWarning($"[线程{threadId}] 响应解析失败: {result.Message}");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logService.LogError($"[线程{threadId}] 读取响应文件 {responseFile} 失败: {ex.Message}");
                        }
                    }
                    else
                    {
                        // 每10次检查记录一次调试信息，避免日志过多
                        if (retryCount % 10 == 0)
                        {
                            _logService.LogInfo($"[线程{threadId}] 第{retryCount}次检查，响应文件 {responseFile} 不存在");
                        }
                    }

                    // 等待500ms后继续检查（增加检查间隔，避免过于频繁）
                    await Task.Delay(500);
                }

                // 超时或达到最大重试次数处理
                var reason = retryCount >= maxRetryCount ? "达到最大重试次数" : "超时";
                _logService.LogWarning($"[线程{threadId}] 邮箱验证码获取失败（{reason}），共尝试{retryCount}次");
                CleanupFiles(requestFile, responseFile, threadId);
                return (false, null, $"邮箱验证码获取失败（{reason}），共尝试{retryCount}次");
            }
            catch (Exception ex)
            {
                _logService.LogError($"[线程{threadId}] 邮箱验证码获取异常: {ex.Message}");
                return (false, null, $"邮箱验证码获取异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析邮箱响应内容
        /// </summary>
        private (bool Success, string? VerificationCode, string Message) ParseEmailResponse(string responseContent, int threadId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(responseContent))
                {
                    return (false, null, "响应内容为空");
                }

                // 检查是否包含线程ID标识
                if (!responseContent.Contains($"ThreadId:{threadId}"))
                {
                    return (false, null, $"响应不匹配当前线程ID {threadId}");
                }

                // 解析验证码
                // 格式示例: ThreadId:1|Code:123456|Status:Success|Time:2024-01-01 12:00:00
                var parts = responseContent.Split('|');
                string? verificationCode = null;
                string status = "Unknown";

                foreach (var part in parts)
                {
                    if (part.StartsWith("Code:"))
                    {
                        verificationCode = part.Substring(5).Trim();
                    }
                    else if (part.StartsWith("Status:"))
                    {
                        status = part.Substring(7).Trim();
                    }
                }

                if (status.Equals("Success", StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(verificationCode))
                {
                    return (true, verificationCode, "邮箱验证码获取成功");
                }
                else if (status.Equals("NotFound", StringComparison.OrdinalIgnoreCase))
                {
                    return (false, null, "未找到验证码邮件");
                }
                else if (status.Equals("Error", StringComparison.OrdinalIgnoreCase))
                {
                    return (false, null, "邮箱验证码获取失败");
                }
                else
                {
                    return (false, null, $"未知状态: {status}");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"[线程{threadId}] 解析邮箱响应异常: {ex.Message}");
                return (false, null, $"解析响应异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理临时文件
        /// </summary>
        private void CleanupFiles(string requestFile, string responseFile, int threadId)
        {
            try
            {
                if (File.Exists(requestFile))
                {
                    File.Delete(requestFile);
                    _logService.LogInfo($"[线程{threadId}] 已清理请求文件");
                }
                if (File.Exists(responseFile))
                {
                    File.Delete(responseFile);
                    _logService.LogInfo($"[线程{threadId}] 已清理响应文件");
                }
            }
            catch (Exception ex)
            {
                _logService.LogWarning($"[线程{threadId}] 清理文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查邮箱验证码服务是否可用
        /// </summary>
        public bool IsServiceAvailable()
        {
            try
            {
                // 检查是否有权限创建和删除文件
                var testFile = $"EmailServiceTest_{DateTime.Now.Ticks}.txt";
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取所有线程的邮箱验证码文件状态
        /// </summary>
        public Dictionary<int, (bool HasRequest, bool HasResponse, DateTime? LastModified)> GetAllThreadFileStatus(int maxThreadId = 6)
        {
            var result = new Dictionary<int, (bool, bool, DateTime?)>();

            for (int threadId = 1; threadId <= maxThreadId; threadId++)
            {
                var requestFile = $"EmailCodeRequest_Thread{threadId}.txt";
                var responseFile = $"EmailCodeResponse_Thread{threadId}.txt";

                var hasRequest = File.Exists(requestFile);
                var hasResponse = File.Exists(responseFile);
                DateTime? lastModified = null;

                try
                {
                    if (hasResponse)
                    {
                        lastModified = File.GetLastWriteTime(responseFile);
                    }
                    else if (hasRequest)
                    {
                        lastModified = File.GetLastWriteTime(requestFile);
                    }
                }
                catch
                {
                    // 忽略文件访问错误
                }

                result[threadId] = (hasRequest, hasResponse, lastModified);
            }

            return result;
        }

        /// <summary>
        /// 清理所有线程的邮箱验证码文件
        /// </summary>
        public void CleanupAllThreadFiles(int maxThreadId = 6)
        {
            for (int threadId = 1; threadId <= maxThreadId; threadId++)
            {
                var requestFile = $"EmailCodeRequest_Thread{threadId}.txt";
                var responseFile = $"EmailCodeResponse_Thread{threadId}.txt";

                CleanupFiles(requestFile, responseFile, threadId);
            }

            _logService.LogInfo($"已清理所有线程（1-{maxThreadId}）的邮箱验证码文件");
        }

        /// <summary>
        /// 强制为指定线程创建成功响应（用于测试或紧急情况）
        /// </summary>
        public async Task<bool> CreateMockResponseForThread(int threadId, string verificationCode)
        {
            try
            {
                var responseFile = $"EmailCodeResponse_Thread{threadId}.txt";
                var responseContent = $"ThreadId:{threadId}|Code:{verificationCode}|Status:Success|Time:{DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                
                await File.WriteAllTextAsync(responseFile, responseContent);
                _logService.LogInfo($"[线程{threadId}] 已创建模拟响应: {verificationCode}");
                return true;
            }
            catch (Exception ex)
            {
                _logService.LogError($"[线程{threadId}] 创建模拟响应失败: {ex.Message}");
                return false;
            }
        }
    }
}
