using System;
using AWSAutoRegister.Services;

namespace AWSAutoRegister
{
    class TestLogService
    {
        static void Main(string[] args)
        {
            Console.WriteLine("测试日志服务...");
            
            var logService = LogService.Instance;
            
            // 测试各种日志记录功能
            logService.LogInfo("程序启动测试");
            logService.LogButtonClick("测试按钮", "测试按钮点击功能");
            logService.LogStatusChange("测试状态变化");
            logService.LogRegistrationStart("测试邮箱: <EMAIL>, 索引: 1/1");
            logService.LogInfo("模拟注册过程中的状态更新");
            logService.LogStatusChange("正在填写第一页信息...");
            logService.LogStatusChange("正在填写第二页信息...");
            logService.LogRegistrationEnd("注册成功完成 - 邮箱: <EMAIL>");
            
            Console.WriteLine($"日志已写入到: {logService.GetLogFilePath()}");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
