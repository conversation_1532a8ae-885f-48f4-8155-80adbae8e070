using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// AWS日志服务 - 专门处理aws.txt文件的写入
    /// </summary>
    public class AwsLogService
    {
        private readonly string _logFilePath;
        private readonly object _lockObject = new object();

        public AwsLogService()
        {
            // 在软件运行目录下创建aws.txt
            _logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "aws.txt");
        }

        /// <summary>
        /// 写入AWS注册数据到aws.txt
        /// </summary>
        /// <param name="clipboardContent">剪贴板内容</param>
        public async Task WriteAwsDataAsync(string clipboardContent)
        {
            await Task.Run(() =>
            {
                try
                {
                    lock (_lockObject)
                    {
                        // 获取当前时间和时间段
                        var now = DateTime.Now;
                        var timeHeader = GetTimeHeader(now);

                        // 读取现有内容
                        var lines = new List<string>();
                        if (File.Exists(_logFilePath))
                        {
                            lines.AddRange(File.ReadAllLines(_logFilePath));
                        }

                        // 检查是否需要添加新的时间段标题
                        bool needNewTimeHeader = true;
                        if (lines.Count > 0)
                        {
                            // 检查最后一个时间段标题是否与当前时间段相同
                            for (int i = lines.Count - 1; i >= 0; i--)
                            {
                                var line = lines[i].Trim();
                                if (IsTimeHeader(line))
                                {
                                    if (line == timeHeader)
                                    {
                                        needNewTimeHeader = false;
                                    }
                                    break;
                                }
                            }
                        }

                        // 添加时间段标题（如果需要）
                        if (needNewTimeHeader)
                        {
                            if (lines.Count > 0)
                            {
                                lines.Add(""); // 空行分隔
                            }
                            lines.Add(timeHeader);
                        }

                        // 添加数据
                        lines.Add(clipboardContent);

                        // 写入文件
                        File.WriteAllLines(_logFilePath, lines);
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但不抛出异常，避免影响主流程
                    Console.WriteLine($"[AwsLogService] 写入aws.txt失败: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 获取时间段标题
        /// </summary>
        private string GetTimeHeader(DateTime dateTime)
        {
            var hour = dateTime.Hour;
            var timeOfDay = GetTimeOfDay(hour);
            return $"{dateTime.Month}-{dateTime.Day} {timeOfDay}";
        }

        /// <summary>
        /// 根据小时获取时间段
        /// </summary>
        private string GetTimeOfDay(int hour)
        {
            return hour switch
            {
                >= 0 and <= 5 => "凌晨",
                >= 6 and <= 11 => "上午", 
                >= 12 and <= 17 => "下午",
                >= 18 and <= 23 => "晚上",
                _ => "上午"
            };
        }

        /// <summary>
        /// 检查是否为时间段标题
        /// </summary>
        private bool IsTimeHeader(string line)
        {
            if (string.IsNullOrWhiteSpace(line))
                return false;

            // 检查格式：数字-数字 时间段
            var parts = line.Split(' ');
            if (parts.Length != 2)
                return false;

            var datePart = parts[0];
            var timePart = parts[1];

            // 检查日期部分是否包含"-"
            if (!datePart.Contains('-'))
                return false;

            // 检查时间段部分
            return timePart == "凌晨" || timePart == "上午" || timePart == "下午" || timePart == "晚上";
        }

        /// <summary>
        /// 获取aws.txt文件路径
        /// </summary>
        public string GetLogFilePath()
        {
            return _logFilePath;
        }
    }
}
