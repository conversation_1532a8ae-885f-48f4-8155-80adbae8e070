using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AWSAutoRegister.Services;

namespace AWSAutoRegister.Controls
{
    public partial class SearchableCountryComboBox : UserControl
    {
        private List<CountryCodeService.CountryInfo> _allCountries = new();
        private CountryCodeService.CountryInfo? _selectedCountry;

        public event EventHandler<CountryCodeService.CountryInfo>? CountrySelected;

        public CountryCodeService.CountryInfo? SelectedCountry
        {
            get => _selectedCountry;
            set
            {
                _selectedCountry = value;
                UpdateDisplayText();
            }
        }

        public SearchableCountryComboBox()
        {
            InitializeComponent();
            LoadCountries();
            
            // 点击外部关闭下拉框
            this.LostFocus += (s, e) => CloseDropDown();
        }

        private void LoadCountries()
        {
            _allCountries = CountryCodeService.GetSupportedCountries();
            CountryListBox.ItemsSource = _allCountries;
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? "";
            
            // 更新占位符可见性
            PlaceholderText.Visibility = string.IsNullOrEmpty(searchText) ? Visibility.Visible : Visibility.Collapsed;
            
            if (string.IsNullOrEmpty(searchText))
            {
                CountryListBox.ItemsSource = _allCountries;
            }
            else
            {
                var filteredCountries = _allCountries.Where(country =>
                    country.Name.ToLower().Contains(searchText) ||
                    country.Code.ToLower().Contains(searchText) ||
                    country.PhoneCode.Contains(searchText)
                ).ToList();
                
                CountryListBox.ItemsSource = filteredCountries;
            }
            
            // 自动打开下拉框
            if (!string.IsNullOrEmpty(searchText))
            {
                OpenDropDown();
            }
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            OpenDropDown();
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // 延迟关闭，允许用户点击列表项
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (!IsKeyboardFocusWithin)
                {
                    CloseDropDown();
                }
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        private void DropDownButton_Click(object sender, RoutedEventArgs e)
        {
            if (DropDownBorder.Visibility == Visibility.Visible)
            {
                CloseDropDown();
            }
            else
            {
                OpenDropDown();
                SearchTextBox.Focus();
            }
        }

        private void CountryListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CountryListBox.SelectedItem is CountryCodeService.CountryInfo selectedCountry)
            {
                SelectedCountry = selectedCountry;
                CountrySelected?.Invoke(this, selectedCountry);
                CloseDropDown();
            }
        }

        private void OpenDropDown()
        {
            DropDownBorder.Visibility = Visibility.Visible;
        }

        private void CloseDropDown()
        {
            DropDownBorder.Visibility = Visibility.Collapsed;
        }

        private void UpdateDisplayText()
        {
            if (_selectedCountry != null)
            {
                SearchTextBox.Text = _selectedCountry.Name;
                PlaceholderText.Visibility = Visibility.Collapsed;
            }
            else
            {
                SearchTextBox.Text = "";
                PlaceholderText.Visibility = Visibility.Visible;
            }
        }

        public void SetSelectedCountryByCode(string countryCode)
        {
            var country = _allCountries.FirstOrDefault(c => 
                c.Code.Equals(countryCode, StringComparison.OrdinalIgnoreCase));
            if (country != null)
            {
                SelectedCountry = country;
            }
        }
    }
}
