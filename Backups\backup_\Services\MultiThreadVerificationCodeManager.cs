using AWSAutoRegister.Models;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 多线程验证码管理器 - 智能批量处理验证码获取
    /// </summary>
    public class MultiThreadVerificationCodeManager
    {
        private static readonly SemaphoreSlim _batchSemaphore = new SemaphoreSlim(1, 1);
        private static readonly Dictionary<int, VerificationRequest> _pendingRequests = new Dictionary<int, VerificationRequest>();
        private static readonly Dictionary<int, VerificationRequest> _waitingRequests = new Dictionary<int, VerificationRequest>();
        private static readonly Timer _batchProcessTimer;
        private static BatchState _currentBatchState = BatchState.Idle;
        private static DateTime _batchStartTime = DateTime.MinValue;
        private static readonly LogService _logService = LogService.Instance;

        static MultiThreadVerificationCodeManager()
        {
            // 每2秒检查一次状态
            _batchProcessTimer = new Timer(CheckBatchState, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
        }

        /// <summary>
        /// 请求验证码（多线程模式）
        /// </summary>
        public static async Task<(bool Success, string? VerificationCode, string Message)> RequestVerificationCodeAsync(string phoneNumber, int threadId)
        {
            var request = new VerificationRequest
            {
                PhoneNumber = phoneNumber,
                ThreadId = threadId,
                RequestTime = DateTime.Now,
                TaskSource = new TaskCompletionSource<(bool, string?, string)>()
            };

            lock (_pendingRequests)
            {
                if (_currentBatchState == BatchState.Collecting)
                {
                    // 收集阶段：加入当前批次
                    _pendingRequests[threadId] = request;
                    _logService.LogInfo($"线程{threadId}加入当前批次，手机号：{phoneNumber}");

                    // 如果是第一个请求，开始计时
                    if (_pendingRequests.Count == 1)
                    {
                        _batchStartTime = DateTime.Now;
                        _logService.LogInfo("开始新批次收集，5秒后开始处理");
                    }
                }
                else if (_currentBatchState == BatchState.Processing)
                {
                    // 处理阶段：加入等待队列，等下一批次
                    _waitingRequests[threadId] = request;
                    _logService.LogInfo($"线程{threadId}加入等待队列，将在下一批次处理，手机号：{phoneNumber}");
                }
                else // Idle
                {
                    // 空闲阶段：立即开始新批次
                    _pendingRequests[threadId] = request;
                    _currentBatchState = BatchState.Collecting;
                    _batchStartTime = DateTime.Now;
                    _logService.LogInfo($"线程{threadId}开始新批次，手机号：{phoneNumber}");
                }
            }

            return await request.TaskSource.Task;
        }

        /// <summary>
        /// 检查批次状态并处理
        /// </summary>
        private static void CheckBatchState(object? state)
        {
            try
            {
                lock (_pendingRequests)
                {
                    if (_currentBatchState == BatchState.Collecting && _pendingRequests.Count > 0)
                    {
                        // 检查是否到了处理时间（5秒后）
                        if (DateTime.Now - _batchStartTime >= TimeSpan.FromSeconds(5))
                        {
                            _currentBatchState = BatchState.Processing;
                            _logService.LogInfo($"批次收集完成，开始处理{_pendingRequests.Count}个验证码请求");

                            // 异步处理当前批次
                            var currentBatch = new List<VerificationRequest>(_pendingRequests.Values);
                            _pendingRequests.Clear();

                            _ = Task.Run(async () =>
                            {
                                await ProcessBatchRequests(currentBatch);

                                // 处理完成后，检查是否有等待的请求
                                lock (_pendingRequests)
                                {
                                    if (_waitingRequests.Count > 0)
                                    {
                                        // 将等待队列的请求移到当前批次
                                        foreach (var kvp in _waitingRequests)
                                        {
                                            _pendingRequests[kvp.Key] = kvp.Value;
                                        }
                                        _waitingRequests.Clear();

                                        _currentBatchState = BatchState.Collecting;
                                        _batchStartTime = DateTime.Now;
                                        _logService.LogInfo($"开始处理等待队列，新批次包含{_pendingRequests.Count}个请求");
                                    }
                                    else
                                    {
                                        _currentBatchState = BatchState.Idle;
                                        _logService.LogInfo("批次处理完成，进入空闲状态");
                                    }
                                }
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"批次状态检查异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量处理验证码请求
        /// </summary>
        private static async Task ProcessBatchRequests(List<VerificationRequest> requests)
        {
            await _batchSemaphore.WaitAsync();
            try
            {
                // 获取当前的手机服务商配置
                var configService = new ConfigService();
                var phoneConfig = configService.PhoneApi;

                if (phoneConfig.Provider == PhoneProvider.Durian)
                {
                    var phoneApiService = ServiceLocator.GetPhoneApiService();
                    if (phoneApiService == null)
                    {
                        _logService.LogError("榴莲PhoneApiService未初始化，无法处理验证码请求");
                        foreach (var request in requests)
                        {
                            request.TaskSource.SetResult((false, null, "榴莲PhoneApiService未初始化"));
                        }
                        return;
                    }

                    // 所有线程一起等待5秒（只等待一次）
                    _logService.LogInfo($"榴莲批次开始：{requests.Count}个线程一起等待5秒后开始获取验证码");
                    await Task.Delay(5000);

                    // 并行处理所有验证码请求
                    var tasks = requests.Select(async request =>
                    {
                        try
                        {
                            _logService.LogInfo($"开始为线程{request.ThreadId}获取榴莲验证码，手机号：{request.PhoneNumber}");

                            // 使用现有的验证码获取逻辑：15秒间隔重试 + 2分钟超时（不再等待5秒）
                            var result = await GetVerificationCodeWithRetryNoDelay(phoneApiService, request.PhoneNumber, request.ThreadId);

                            request.TaskSource.SetResult(result);
                            _logService.LogInfo($"线程{request.ThreadId}榴莲验证码获取完成: {result.Success}");
                        }
                        catch (Exception ex)
                        {
                            request.TaskSource.SetException(ex);
                            _logService.LogError($"线程{request.ThreadId}榴莲验证码获取异常: {ex.Message}");
                        }
                    });

                    await Task.WhenAll(tasks);
                    _logService.LogInfo($"榴莲批次处理完成，共处理{requests.Count}个验证码请求");
                }
                else if (phoneConfig.Provider == PhoneProvider.Qianchuan)
                {
                    var qianchuanPhoneApiService = ServiceLocator.GetQianchuanPhoneApiService();
                    if (qianchuanPhoneApiService == null)
                    {
                        _logService.LogError("千川PhoneApiService未初始化，无法处理验证码请求");
                        foreach (var request in requests)
                        {
                            request.TaskSource.SetResult((false, null, "千川PhoneApiService未初始化"));
                        }
                        return;
                    }

                    // 所有线程一起等待5秒（只等待一次）
                    _logService.LogInfo($"千川批次开始：{requests.Count}个线程一起等待5秒后开始获取验证码");
                    await Task.Delay(5000);

                    // 并行处理所有验证码请求
                    var tasks = requests.Select(async request =>
                    {
                        try
                        {
                            _logService.LogInfo($"开始为线程{request.ThreadId}获取千川验证码，手机号：{request.PhoneNumber}");

                            // 使用千川的验证码获取逻辑
                            var result = await GetQianchuanVerificationCodeWithRetryNoDelay(qianchuanPhoneApiService, request.PhoneNumber, request.ThreadId);

                            request.TaskSource.SetResult(result);
                            _logService.LogInfo($"线程{request.ThreadId}千川验证码获取完成: {result.Success}");
                        }
                        catch (Exception ex)
                        {
                            request.TaskSource.SetException(ex);
                            _logService.LogError($"线程{request.ThreadId}千川验证码获取异常: {ex.Message}");
                        }
                    });

                    await Task.WhenAll(tasks);
                    _logService.LogInfo($"千川批次处理完成，共处理{requests.Count}个验证码请求");
                }
                else
                {
                    _logService.LogError($"不支持的手机服务商类型: {phoneConfig.Provider}");
                    foreach (var request in requests)
                    {
                        request.TaskSource.SetResult((false, null, $"不支持的手机服务商类型: {phoneConfig.Provider}"));
                    }
                }
            }
            finally
            {
                _batchSemaphore.Release();
            }
        }

        /// <summary>
        /// 使用现有逻辑获取验证码（无延迟版本，因为批次已经统一等待了5秒）
        /// </summary>
        private static async Task<(bool Success, string? VerificationCode, string Message)> GetVerificationCodeWithRetryNoDelay(
            PhoneApiService phoneApiService, string phoneNumber, int threadId)
        {
            try
            {
                // 不再等待5秒，因为批次处理时已经统一等待了
                var attempts = 0;
                var maxAttempts = 5; // 最多5次尝试，或者2分钟超时
                var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMinutes(2));

                while (attempts < maxAttempts && !cancellationTokenSource.Token.IsCancellationRequested)
                {
                    attempts++;
                    _logService.LogInfo($"线程{threadId}第{attempts}次尝试获取榴莲验证码...（剩余{maxAttempts - attempts}次尝试）");

                    var result = await phoneApiService.GetVerificationCodeAsync(phoneNumber);

                    if (result.Success && !string.IsNullOrEmpty(result.VerificationCode))
                    {
                        _logService.LogInfo($"线程{threadId}榴莲验证码获取成功: {result.VerificationCode}");

                        // 多线程模式：添加到释放队列（替代黑名单）
                        PhoneNumberReleaseManager.AddToReleaseQueue(phoneNumber, threadId, "获取验证码成功");

                        return result;
                    }
                    else if (result.Message.Contains("暂未查询到验证码"))
                    {
                        _logService.LogInfo($"线程{threadId}第{attempts}次获取榴莲验证码失败，10秒后重试...（剩余{maxAttempts - attempts}次尝试）");

                        // 10秒倒计时等待
                        for (int i = 10; i > 0; i--)
                        {
                            if (cancellationTokenSource.Token.IsCancellationRequested)
                            {
                                return (false, null, "验证码获取已被取消");
                            }
                            await Task.Delay(1000, cancellationTokenSource.Token);
                        }
                    }
                    else
                    {
                        _logService.LogError($"线程{threadId}获取榴莲验证码失败: {result.Message}");
                        return result;
                    }
                }

                // 超时或失败
                _logService.LogWarning($"线程{threadId}榴莲验证码获取超时失败");

                // 多线程模式：添加到释放队列
                PhoneNumberReleaseManager.AddToReleaseQueue(phoneNumber, threadId, "验证码获取超时失败");

                return (false, null, "验证码获取超时失败");
            }
            catch (Exception ex)
            {
                _logService.LogError($"线程{threadId}榴莲验证码获取异常: {ex.Message}");
                return (false, null, $"验证码获取异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 千川验证码获取重试逻辑（无延迟版本，因为批次已经统一等待了5秒）
        /// </summary>
        private static async Task<(bool Success, string? VerificationCode, string Message)> GetQianchuanVerificationCodeWithRetryNoDelay(
            QianchuanPhoneApiService qianchuanPhoneApiService, string phoneNumber, int threadId)
        {
            try
            {
                // 不再等待5秒，因为批次处理时已经统一等待了
                var attempts = 0;
                var maxAttempts = 5; // 最多5次尝试，或者2分钟超时
                var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMinutes(2));

                while (attempts < maxAttempts && !cancellationTokenSource.Token.IsCancellationRequested)
                {
                    attempts++;
                    _logService.LogInfo($"线程{threadId}第{attempts}次尝试获取千川验证码...（剩余{maxAttempts - attempts}次尝试）");

                    var result = await qianchuanPhoneApiService.GetVerificationCodeAsync(phoneNumber);

                    if (result.Success && !string.IsNullOrEmpty(result.VerificationCode))
                    {
                        _logService.LogInfo($"线程{threadId}千川验证码获取成功: {result.VerificationCode}");

                        // 多线程模式：添加到黑名单队列
                        QianchuanPhoneNumberBlacklistManager.AddToBlacklistQueue(phoneNumber, threadId, "获取验证码成功");

                        return result;
                    }
                    else if (result.Message.Contains("暂未查询到验证码"))
                    {
                        _logService.LogInfo($"线程{threadId}第{attempts}次获取千川验证码失败，10秒后重试...（剩余{maxAttempts - attempts}次尝试）");

                        // 10秒倒计时等待
                        for (int i = 10; i > 0; i--)
                        {
                            if (cancellationTokenSource.Token.IsCancellationRequested)
                            {
                                return (false, null, "验证码获取已被取消");
                            }
                            await Task.Delay(1000, cancellationTokenSource.Token);
                        }
                    }
                    else
                    {
                        _logService.LogError($"线程{threadId}获取千川验证码失败: {result.Message}");
                        return result;
                    }
                }

                // 超时或失败
                _logService.LogWarning($"线程{threadId}千川验证码获取超时失败");

                // 多线程模式：添加到黑名单队列
                QianchuanPhoneNumberBlacklistManager.AddToBlacklistQueue(phoneNumber, threadId, "验证码获取超时失败");

                return (false, null, "验证码获取超时失败");
            }
            catch (Exception ex)
            {
                _logService.LogError($"线程{threadId}千川验证码获取异常: {ex.Message}");
                return (false, null, $"验证码获取异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前批次状态信息
        /// </summary>
        public static (BatchState State, int PendingCount, int WaitingCount, DateTime BatchStartTime) GetBatchStatus()
        {
            lock (_pendingRequests)
            {
                return (_currentBatchState, _pendingRequests.Count, _waitingRequests.Count, _batchStartTime);
            }
        }

        /// <summary>
        /// 清理所有待处理的请求（程序退出时调用）
        /// </summary>
        public static void ClearAllRequests()
        {
            lock (_pendingRequests)
            {
                // 取消所有待处理的请求
                foreach (var request in _pendingRequests.Values)
                {
                    request.TaskSource.SetCanceled();
                }
                foreach (var request in _waitingRequests.Values)
                {
                    request.TaskSource.SetCanceled();
                }

                _pendingRequests.Clear();
                _waitingRequests.Clear();
                _currentBatchState = BatchState.Idle;

                _logService.LogInfo("已清理所有验证码请求");
            }
        }

        /// <summary>
        /// 强制处理当前批次（用于调试或紧急情况）
        /// </summary>
        public static void ForceBatchProcessing()
        {
            lock (_pendingRequests)
            {
                if (_currentBatchState == BatchState.Collecting && _pendingRequests.Count > 0)
                {
                    _currentBatchState = BatchState.Processing;
                    _logService.LogInfo($"强制处理当前批次，包含{_pendingRequests.Count}个请求");

                    var currentBatch = new List<VerificationRequest>(_pendingRequests.Values);
                    _pendingRequests.Clear();

                    _ = Task.Run(() => ProcessBatchRequests(currentBatch));
                }
            }
        }
    }

    /// <summary>
    /// 服务定位器（简单实现）
    /// </summary>
    public static class ServiceLocator
    {
        private static PhoneApiService? _phoneApiService;
        private static QianchuanPhoneApiService? _qianchuanPhoneApiService;

        public static void SetPhoneApiService(PhoneApiService phoneApiService)
        {
            _phoneApiService = phoneApiService;
        }

        public static PhoneApiService? GetPhoneApiService()
        {
            return _phoneApiService;
        }

        public static void SetQianchuanPhoneApiService(QianchuanPhoneApiService qianchuanPhoneApiService)
        {
            _qianchuanPhoneApiService = qianchuanPhoneApiService;
        }

        public static QianchuanPhoneApiService? GetQianchuanPhoneApiService()
        {
            return _qianchuanPhoneApiService;
        }
    }

    /// <summary>
    /// 手机号码统一释放管理器
    /// </summary>
    public static class PhoneNumberReleaseManager
    {
        private static readonly SemaphoreSlim _releaseSemaphore = new SemaphoreSlim(1, 1);
        private static readonly HashSet<string> _pendingReleaseNumbers = new HashSet<string>();
        private static readonly Timer _batchReleaseTimer;
        private static PhoneApiService? _phoneApiService;
        private static readonly LogService _logService = LogService.Instance;

        static PhoneNumberReleaseManager()
        {
            // 每30秒检查一次是否有需要释放的号码
            _batchReleaseTimer = new Timer(ProcessBatchRelease, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        }

        /// <summary>
        /// 设置手机API服务
        /// </summary>
        public static void SetPhoneApiService(PhoneApiService phoneApiService)
        {
            _phoneApiService = phoneApiService;
        }

        /// <summary>
        /// 添加手机号码到释放队列（获取到验证码时调用）
        /// </summary>
        public static void AddToReleaseQueue(string phoneNumber, int threadId, string reason = "获取验证码成功")
        {
            lock (_pendingReleaseNumbers)
            {
                if (_pendingReleaseNumbers.Add(phoneNumber))
                {
                    _logService.LogInfo($"线程{threadId}手机号码已加入释放队列: {phoneNumber} (原因: {reason})");
                }
            }
        }

        /// <summary>
        /// 添加多个手机号码到释放队列（超时或失败时调用）
        /// </summary>
        public static void AddBatchToReleaseQueue(List<string> phoneNumbers, string reason = "验证码获取超时")
        {
            lock (_pendingReleaseNumbers)
            {
                int addedCount = 0;
                foreach (var phoneNumber in phoneNumbers)
                {
                    if (_pendingReleaseNumbers.Add(phoneNumber))
                    {
                        addedCount++;
                    }
                }

                if (addedCount > 0)
                {
                    _logService.LogInfo($"批量添加{addedCount}个手机号码到释放队列 (原因: {reason})");
                }
            }
        }

        /// <summary>
        /// 立即释放所有待释放的手机号码
        /// </summary>
        public static async Task<(int SuccessCount, int FailCount, string Message)> ReleaseAllPendingNumbers()
        {
            await _releaseSemaphore.WaitAsync();
            try
            {
                List<string> numbersToRelease;
                lock (_pendingReleaseNumbers)
                {
                    numbersToRelease = new List<string>(_pendingReleaseNumbers);
                    _pendingReleaseNumbers.Clear();
                }

                if (numbersToRelease.Count == 0)
                {
                    return (0, 0, "没有需要释放的手机号码");
                }

                if (_phoneApiService == null)
                {
                    _logService.LogError("手机API服务未设置，无法释放手机号码");
                    return (0, numbersToRelease.Count, "手机API服务未设置");
                }

                _logService.LogInfo($"开始释放{numbersToRelease.Count}个手机号码");
                var result = await _phoneApiService.ReleaseBatchPhoneNumbersAsync(numbersToRelease);

                return result;
            }
            finally
            {
                _releaseSemaphore.Release();
            }
        }

        /// <summary>
        /// 定时批量释放处理
        /// </summary>
        private static async void ProcessBatchRelease(object? state)
        {
            try
            {
                int pendingCount;
                lock (_pendingReleaseNumbers)
                {
                    pendingCount = _pendingReleaseNumbers.Count;
                }

                if (pendingCount > 0)
                {
                    _logService.LogInfo($"定时检查发现{pendingCount}个待释放手机号码，开始批量释放");
                    var result = await ReleaseAllPendingNumbers();
                    _logService.LogInfo($"定时批量释放完成: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"定时批量释放异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前待释放号码数量
        /// </summary>
        public static int GetPendingReleaseCount()
        {
            lock (_pendingReleaseNumbers)
            {
                return _pendingReleaseNumbers.Count;
            }
        }

        /// <summary>
        /// 清理所有待释放号码（程序退出时调用）
        /// </summary>
        public static async Task CleanupOnExit()
        {
            _logService.LogInfo("程序退出，开始清理所有待释放手机号码");
            var result = await ReleaseAllPendingNumbers();
            _logService.LogInfo($"程序退出清理完成: {result.Message}");
        }

        /// <summary>
        /// 请求美国API验证码（多线程模式）
        /// </summary>
        public static async Task<(bool Success, string? VerificationCode, string Message)> RequestUsaVerificationCodeAsync(string apiUrl, int threadId)
        {
            try
            {
                _logService.LogInfo($"线程{threadId}开始获取美国API验证码，API URL: {apiUrl}");

                // 美国API不需要批量处理，直接获取
                var usaPhoneApiService = new UsaPhoneApiService("https://www.api21k.com", "A28AFF657DC03E5B4E165400E3F1");

                // 等待5秒后开始获取（与榴莲API保持一致）
                await Task.Delay(5000);

                var result = await GetUsaVerificationCodeWithRetry(usaPhoneApiService, apiUrl, threadId);

                _logService.LogInfo($"线程{threadId}美国API验证码获取完成: {result.Success}");
                return result;
            }
            catch (Exception ex)
            {
                _logService.LogError($"线程{threadId}美国API验证码获取异常: {ex.Message}");
                return (false, null, $"验证码获取异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 美国API验证码获取重试逻辑
        /// </summary>
        private static async Task<(bool Success, string? VerificationCode, string Message)> GetUsaVerificationCodeWithRetry(
            UsaPhoneApiService usaPhoneApiService, string apiUrl, int threadId)
        {
            try
            {
                var attempts = 0;
                var maxAttempts = 5; // 最多5次尝试，或者2分钟超时
                var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMinutes(2));

                while (attempts < maxAttempts && !cancellationTokenSource.Token.IsCancellationRequested)
                {
                    attempts++;
                    _logService.LogInfo($"线程{threadId}第{attempts}次尝试获取美国API验证码...（剩余{maxAttempts - attempts}次尝试）");

                    var result = await usaPhoneApiService.GetVerificationCodeAsync(apiUrl);

                    if (result.Success && !string.IsNullOrEmpty(result.VerificationCode))
                    {
                        _logService.LogInfo($"线程{threadId}美国API验证码获取成功: {result.VerificationCode}");
                        return result;
                    }
                    else if (result.Message.Contains("未找到验证码"))
                    {
                        _logService.LogInfo($"线程{threadId}第{attempts}次获取美国API验证码失败，10秒后重试...（剩余{maxAttempts - attempts}次尝试）");

                        // 10秒倒计时等待
                        for (int i = 10; i > 0; i--)
                        {
                            if (cancellationTokenSource.Token.IsCancellationRequested)
                            {
                                return (false, null, "验证码获取已被取消");
                            }
                            await Task.Delay(1000, cancellationTokenSource.Token);
                        }
                    }
                    else
                    {
                        _logService.LogError($"线程{threadId}获取美国API验证码失败: {result.Message}");
                        return result;
                    }
                }

                // 超时或失败
                _logService.LogWarning($"线程{threadId}美国API验证码获取超时失败");
                return (false, null, "验证码获取超时失败");
            }
            catch (Exception ex)
            {
                _logService.LogError($"线程{threadId}美国API验证码获取异常: {ex.Message}");
                return (false, null, $"验证码获取异常: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 千川手机号码统一黑名单管理器
    /// </summary>
    public static class QianchuanPhoneNumberBlacklistManager
    {
        private static readonly SemaphoreSlim _blacklistSemaphore = new SemaphoreSlim(1, 1);
        private static readonly HashSet<string> _pendingBlacklistNumbers = new HashSet<string>();
        private static readonly Timer _batchBlacklistTimer;
        private static QianchuanPhoneApiService? _qianchuanPhoneApiService;
        private static readonly LogService _logService = LogService.Instance;

        static QianchuanPhoneNumberBlacklistManager()
        {
            // 每30秒检查一次是否有需要拉黑的号码
            _batchBlacklistTimer = new Timer(ProcessBatchBlacklist, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        }

        /// <summary>
        /// 设置千川手机API服务
        /// </summary>
        public static void SetQianchuanPhoneApiService(QianchuanPhoneApiService qianchuanPhoneApiService)
        {
            _qianchuanPhoneApiService = qianchuanPhoneApiService;
        }

        /// <summary>
        /// 添加手机号码到黑名单队列（获取到验证码时调用）
        /// </summary>
        public static void AddToBlacklistQueue(string phoneNumber, int threadId, string reason = "获取验证码成功")
        {
            lock (_pendingBlacklistNumbers)
            {
                if (_pendingBlacklistNumbers.Add(phoneNumber))
                {
                    _logService.LogInfo($"线程{threadId}千川手机号码已加入黑名单队列: {phoneNumber} (原因: {reason})");
                }
            }
        }

        /// <summary>
        /// 添加多个手机号码到黑名单队列（超时或失败时调用）
        /// </summary>
        public static void AddBatchToBlacklistQueue(List<string> phoneNumbers, string reason = "验证码获取超时")
        {
            lock (_pendingBlacklistNumbers)
            {
                int addedCount = 0;
                foreach (var phoneNumber in phoneNumbers)
                {
                    if (_pendingBlacklistNumbers.Add(phoneNumber))
                    {
                        addedCount++;
                    }
                }

                if (addedCount > 0)
                {
                    _logService.LogInfo($"批量添加{addedCount}个千川手机号码到黑名单队列 (原因: {reason})");
                }
            }
        }

        /// <summary>
        /// 立即拉黑所有待拉黑的手机号码
        /// </summary>
        public static async Task<(int SuccessCount, int FailCount, string Message)> BlacklistAllPendingNumbers()
        {
            await _blacklistSemaphore.WaitAsync();
            try
            {
                List<string> numbersToBlacklist;
                lock (_pendingBlacklistNumbers)
                {
                    numbersToBlacklist = new List<string>(_pendingBlacklistNumbers);
                    _pendingBlacklistNumbers.Clear();
                }

                if (numbersToBlacklist.Count == 0)
                {
                    return (0, 0, "没有需要拉黑的千川手机号码");
                }

                if (_qianchuanPhoneApiService == null)
                {
                    _logService.LogError("千川手机API服务未设置，无法拉黑手机号码");
                    return (0, numbersToBlacklist.Count, "千川手机API服务未设置");
                }

                _logService.LogInfo($"开始拉黑{numbersToBlacklist.Count}个千川手机号码");

                int successCount = 0;
                int failCount = 0;
                var messages = new List<string>();

                foreach (var phoneNumber in numbersToBlacklist)
                {
                    try
                    {
                        var result = await _qianchuanPhoneApiService.AddToBlacklistAsync(phoneNumber);
                        if (result.Success)
                        {
                            successCount++;
                        }
                        else
                        {
                            failCount++;
                            messages.Add($"{phoneNumber}: {result.Message}");
                        }

                        // 每个拉黑请求之间等待500ms，避免API频率限制
                        await Task.Delay(500);
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        messages.Add($"{phoneNumber}: 异常 - {ex.Message}");
                    }
                }

                var summary = $"千川批量拉黑完成: 成功{successCount}个, 失败{failCount}个";
                if (messages.Count > 0)
                {
                    summary += $"\n失败详情: {string.Join("; ", messages)}";
                }

                _logService.LogInfo($"[千川API] {summary}");
                return (successCount, failCount, summary);
            }
            finally
            {
                _blacklistSemaphore.Release();
            }
        }

        /// <summary>
        /// 定时批量拉黑处理
        /// </summary>
        private static async void ProcessBatchBlacklist(object? state)
        {
            try
            {
                int pendingCount;
                lock (_pendingBlacklistNumbers)
                {
                    pendingCount = _pendingBlacklistNumbers.Count;
                }

                if (pendingCount > 0)
                {
                    _logService.LogInfo($"定时检查发现{pendingCount}个待拉黑千川手机号码，开始批量拉黑");
                    var result = await BlacklistAllPendingNumbers();
                    _logService.LogInfo($"定时批量拉黑完成: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"定时批量拉黑异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前待拉黑号码数量
        /// </summary>
        public static int GetPendingBlacklistCount()
        {
            lock (_pendingBlacklistNumbers)
            {
                return _pendingBlacklistNumbers.Count;
            }
        }

        /// <summary>
        /// 清理所有待拉黑号码（程序退出时调用）
        /// </summary>
        public static async Task CleanupOnExit()
        {
            _logService.LogInfo("程序退出，开始清理所有待拉黑千川手机号码");
            var result = await BlacklistAllPendingNumbers();
            _logService.LogInfo($"程序退出清理完成: {result.Message}");
        }
    }
}
