using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AWSAutoRegister.Models;
using AWSAutoRegister.Services;

namespace AWSAutoRegister
{
    /// <summary>
    /// 多线程功能测试运行器
    /// </summary>
    public static class TestRunner
    {
        /// <summary>
        /// 运行多线程功能集成测试
        /// </summary>
        public static void RunMultiThreadTests()
        {
            try
            {
                Console.WriteLine("=== AWS自动注册工具 - 多线程功能集成测试 ===");
                Console.WriteLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine();

                var logService = LogService.Instance;
                logService.LogInfo("开始运行多线程功能集成测试");

                var integrationTest = new MultiThreadIntegrationTest();
                var success = integrationTest.RunAllTests();

                Console.WriteLine();
                if (success)
                {
                    Console.WriteLine("🎉 所有测试通过！多线程功能已准备就绪。");
                    logService.LogInfo("多线程功能集成测试全部通过");
                }
                else
                {
                    Console.WriteLine("⚠️ 部分测试失败，请检查日志文件获取详细信息。");
                    logService.LogError("多线程功能集成测试存在失败项");
                }

                Console.WriteLine();
                Console.WriteLine("测试完成，按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试运行器异常: {ex.Message}");
                LogService.Instance.LogError($"测试运行器异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证多线程功能需求符合性
        /// </summary>
        public static bool ValidateMultiThreadRequirements()
        {
            var logService = LogService.Instance;
            logService.LogInfo("开始验证多线程功能需求符合性...");

            var requirements = new List<(string Name, Func<bool> Validator)>
            {
                ("核心数据模型和枚举", ValidateDataModels),
                ("多线程管理器", ValidateMultiThreadManager),
                ("注册线程实例", ValidateRegistrationThread),
                ("多线程管理窗口", ValidateMultiThreadWindow),
                ("手机API多线程处理", ValidatePhoneApiMultiThread),
                ("邮箱验证码多线程适配", ValidateEmailMultiThread),
                ("弹窗处理机制", ValidateDialogManager),
                ("主窗口界面扩展", ValidateMainWindowExtensions),
                ("AutomationService多线程适配", ValidateAutomationServiceMultiThread)
            };

            int passedCount = 0;
            foreach (var (name, validator) in requirements)
            {
                try
                {
                    if (validator())
                    {
                        passedCount++;
                        logService.LogInfo($"✅ {name} - 需求符合");
                    }
                    else
                    {
                        logService.LogError($"❌ {name} - 需求不符合");
                    }
                }
                catch (Exception ex)
                {
                    logService.LogError($"❌ {name} - 验证异常: {ex.Message}");
                }
            }

            var success = passedCount == requirements.Count;
            logService.LogInfo($"需求符合性验证完成: {passedCount}/{requirements.Count}");
            return success;
        }

        // 各项需求验证方法
        private static bool ValidateDataModels()
        {
            try
            {
                // 验证ThreadStatus枚举
                var status = ThreadStatus.Processing;
                if (status.ToString() != "Processing") return false;

                // 验证ThreadStatusInfo类
                var statusInfo = new ThreadStatusInfo();
                statusInfo.Status = ThreadStatus.Processing;
                if (statusInfo.StatusText != "处理中") return false;

                // 验证PhoneNumberInfo类
                var phoneInfo = new PhoneNumberInfo
                {
                    FullNumber = "1234567890",
                    CountryCode = "1",
                    LocalNumber = "234567890"
                };
                if (string.IsNullOrEmpty(phoneInfo.FullNumber)) return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidateMultiThreadManager()
        {
            try
            {
                var configService = new ConfigService();
                var manager = new MultiThreadManager(configService);
                return manager != null;
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidateRegistrationThread()
        {
            try
            {
                var configService = new ConfigService();
                var position = new WindowPosition { X = 0, Y = 0, Width = 500, Height = 500 };
                var fingerprint = new BrowserFingerprint { UserAgent = "test", TimeZone = "UTC" };
                var thread = new RegistrationThread(1, configService, position, fingerprint);
                return thread != null && thread.ThreadId == 1;
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidateMultiThreadWindow()
        {
            try
            {
                // 检查MultiThreadWindow类是否存在
                var type = typeof(MultiThreadWindow);
                return type != null;
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidatePhoneApiMultiThread()
        {
            try
            {
                // 检查MultiThreadPhoneNumberManager类
                var phoneApiService = new PhoneApiService("http://test", "test", "test", "test");
                var manager = new MultiThreadPhoneNumberManager(phoneApiService);
                return manager != null;
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidateEmailMultiThread()
        {
            try
            {
                var service = new MultiThreadEmailVerificationService();
                return service != null;
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidateDialogManager()
        {
            try
            {
                var isMultiThread = DialogManager.IsMultiThreadMode(1);
                var isSingleThread = DialogManager.IsMultiThreadMode(0);
                return isMultiThread && !isSingleThread;
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidateMainWindowExtensions()
        {
            try
            {
                // 检查MainWindow是否包含多线程相关字段
                var type = typeof(MainWindow);
                var fields = type.GetFields(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                return fields.Any(f => f.Name.Contains("multiThread") || f.Name.Contains("MultiThread"));
            }
            catch
            {
                return false;
            }
        }

        private static bool ValidateAutomationServiceMultiThread()
        {
            try
            {
                var service = new AutomationService();
                service.SetThreadId(1);
                return service.GetThreadId() == 1 && service.IsMultiThreadMode();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 生成功能验证报告
        /// </summary>
        public static void GenerateValidationReport()
        {
            var logService = LogService.Instance;
            var reportPath = "MultiThreadValidationReport.txt";

            try
            {
                var report = new List<string>
                {
                    "=== AWS自动注册工具 - 多线程功能验证报告 ===",
                    $"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    "",
                    "## 功能需求验证结果",
                    ""
                };

                // 运行验证
                var success = ValidateMultiThreadRequirements();
                
                report.Add($"总体验证结果: {(success ? "✅ 通过" : "❌ 失败")}");
                report.Add("");

                // 运行集成测试
                report.Add("## 集成测试结果");
                report.Add("");
                
                var integrationTest = new MultiThreadIntegrationTest();
                var testSuccess = integrationTest.RunAllTests();
                
                report.Add($"集成测试结果: {(testSuccess ? "✅ 通过" : "❌ 失败")}");
                report.Add("");

                // 功能清单
                report.Add("## 已实现功能清单");
                report.Add("");
                report.Add("1. ✅ 核心数据模型和枚举扩展");
                report.Add("   - ThreadStatus枚举");
                report.Add("   - ThreadStatusInfo类");
                report.Add("   - PhoneNumberInfo类");
                report.Add("   - WindowPosition类");
                report.Add("   - BrowserFingerprint类");
                report.Add("");
                report.Add("2. ✅ 多线程管理器 (MultiThreadManager)");
                report.Add("   - 统一管理所有注册线程");
                report.Add("   - 数据安全分配");
                report.Add("   - 窗口位置计算");
                report.Add("   - 浏览器指纹生成");
                report.Add("");
                report.Add("3. ✅ 注册线程实例 (RegistrationThread)");
                report.Add("   - 完全复制AutomationService功能");
                report.Add("   - 独立的生命周期管理");
                report.Add("   - 事件驱动的状态通知");
                report.Add("");
                report.Add("4. ✅ 多线程管理窗口 (MultiThreadWindow)");
                report.Add("   - 可视化线程状态管理");
                report.Add("   - 实时进度监控");
                report.Add("   - 线程控制操作");
                report.Add("");
                report.Add("5. ✅ 手机API多线程处理");
                report.Add("   - 批量手机号码获取");
                report.Add("   - 智能验证码批量处理");
                report.Add("   - 统一释放管理");
                report.Add("");
                report.Add("6. ✅ 邮箱验证码多线程适配");
                report.Add("   - 线程ID标识精确匹配");
                report.Add("   - 独立文件处理");
                report.Add("   - 线程安全操作");
                report.Add("");
                report.Add("7. ✅ 弹窗处理机制改造");
                report.Add("   - DialogManager统一管理");
                report.Add("   - 单线程保持原有弹窗");
                report.Add("   - 多线程改为状态显示");
                report.Add("");
                report.Add("8. ✅ MainWindow界面扩展");
                report.Add("   - 线程数量选择下拉框");
                report.Add("   - 浏览器模式互斥控制");
                report.Add("   - 多线程管理窗口入口");
                report.Add("");
                report.Add("9. ✅ AutomationService多线程适配");
                report.Add("   - 线程ID设置和获取");
                report.Add("   - 多线程模式检测");
                report.Add("   - 服务注入方法");
                report.Add("");

                // 写入报告文件
                File.WriteAllLines(reportPath, report);
                
                logService.LogInfo($"功能验证报告已生成: {reportPath}");
                Console.WriteLine($"功能验证报告已生成: {reportPath}");
            }
            catch (Exception ex)
            {
                logService.LogError($"生成验证报告失败: {ex.Message}");
            }
        }
    }
}
