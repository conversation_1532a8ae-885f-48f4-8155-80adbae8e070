using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using AWSAutoRegister.Models;

namespace AWSAutoRegister
{
    /// <summary>
    /// 线程状态到颜色的转换器
    /// </summary>
    public class StatusToColorConverter : IValueConverter
    {
        public static readonly StatusToColorConverter Instance = new StatusToColorConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ThreadStatus status)
            {
                return status switch
                {
                    ThreadStatus.NotStarted => new SolidColorBrush(Color.FromRgb(160, 174, 192)), // 灰色
                    ThreadStatus.Initializing => new SolidColorBrush(Color.FromRgb(66, 153, 225)), // 蓝色
                    ThreadStatus.Processing => new SolidColorBrush(Color.FromRgb(72, 187, 120)), // 绿色
                    ThreadStatus.WaitingForManualAction => new SolidColorBrush(Color.FromRgb(229, 62, 62)), // 红色
                    ThreadStatus.Paused => new SolidColorBrush(Color.FromRgb(237, 137, 54)), // 橙色
                    ThreadStatus.Completed => new SolidColorBrush(Color.FromRgb(72, 187, 120)), // 绿色
                    ThreadStatus.CompletedWithIssues => new SolidColorBrush(Color.FromRgb(66, 153, 225)), // 蓝色
                    ThreadStatus.Failed => new SolidColorBrush(Color.FromRgb(229, 62, 62)), // 红色
                    ThreadStatus.Terminated => new SolidColorBrush(Color.FromRgb(113, 128, 150)), // 深灰色
                    _ => new SolidColorBrush(Color.FromRgb(160, 174, 192)) // 默认灰色
                };
            }
            return new SolidColorBrush(Color.FromRgb(160, 174, 192));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到可见性的转换器
    /// </summary>
    public class BoolToVisibilityConverter : IValueConverter
    {
        public static readonly BoolToVisibilityConverter Instance = new BoolToVisibilityConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            return false;
        }
    }

    /// <summary>
    /// 进度值到宽度的转换器
    /// </summary>
    public class ProgressToWidthConverter : IValueConverter
    {
        public static readonly ProgressToWidthConverter Instance = new ProgressToWidthConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double progress && parameter is FrameworkElement element)
            {
                var maxWidth = element.ActualWidth;
                return (progress / 100.0) * maxWidth;
            }
            return 0.0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 线程状态到状态文本的转换器
    /// </summary>
    public class ThreadStatusToTextConverter : IValueConverter
    {
        public static readonly ThreadStatusToTextConverter Instance = new ThreadStatusToTextConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ThreadStatus status)
            {
                return status switch
                {
                    ThreadStatus.NotStarted => "未开始",
                    ThreadStatus.Initializing => "初始化中",
                    ThreadStatus.Processing => "处理中",
                    ThreadStatus.WaitingForManualAction => "等待手动操作",
                    ThreadStatus.Paused => "已暂停",
                    ThreadStatus.Completed => "已完成",
                    ThreadStatus.CompletedWithIssues => "注册完成",
                    ThreadStatus.Failed => "失败",
                    ThreadStatus.Terminated => "已终止",
                    _ => "未知状态"
                };
            }
            return "未知状态";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 线程状态到图标的转换器
    /// </summary>
    public class ThreadStatusToIconConverter : IValueConverter
    {
        public static readonly ThreadStatusToIconConverter Instance = new ThreadStatusToIconConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ThreadStatus status)
            {
                return status switch
                {
                    ThreadStatus.NotStarted => "⚪",
                    ThreadStatus.Initializing => "🔄",
                    ThreadStatus.Processing => "⚡",
                    ThreadStatus.WaitingForManualAction => "⚠️",
                    ThreadStatus.Paused => "⏸️",
                    ThreadStatus.Completed => "✅",
                    ThreadStatus.CompletedWithIssues => "🔵",
                    ThreadStatus.Failed => "❌",
                    ThreadStatus.Terminated => "⏹️",
                    _ => "❓"
                };
            }
            return "❓";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 数字到百分比文本的转换器
    /// </summary>
    public class NumberToPercentageConverter : IValueConverter
    {
        public static readonly NumberToPercentageConverter Instance = new NumberToPercentageConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double number)
            {
                return $"{number:F0}%";
            }
            if (value is int intNumber)
            {
                return $"{intNumber}%";
            }
            return "0%";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string text && text.EndsWith("%"))
            {
                var numberText = text.Substring(0, text.Length - 1);
                if (double.TryParse(numberText, out var result))
                {
                    return result;
                }
            }
            return 0.0;
        }
    }

    /// <summary>
    /// 线程ID到显示文本的转换器
    /// </summary>
    public class ThreadIdToDisplayConverter : IValueConverter
    {
        public static readonly ThreadIdToDisplayConverter Instance = new ThreadIdToDisplayConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int threadId)
            {
                return $"线程 {threadId}";
            }
            return "未知线程";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 时间戳到显示文本的转换器
    /// </summary>
    public class TimestampToDisplayConverter : IValueConverter
    {
        public static readonly TimestampToDisplayConverter Instance = new TimestampToDisplayConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DateTime dateTime)
            {
                if (dateTime == DateTime.MinValue)
                {
                    return "--:--";
                }
                return dateTime.ToString("HH:mm:ss");
            }
            return "--:--";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 多值转换器：线程状态和需要用户操作到按钮可见性
    /// </summary>
    public class ThreadStatusToButtonVisibilityConverter : IMultiValueConverter
    {
        public static readonly ThreadStatusToButtonVisibilityConverter Instance = new ThreadStatusToButtonVisibilityConverter();

        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 2 && 
                values[0] is ThreadStatus status && 
                values[1] is bool needsUserAction)
            {
                // 只有在等待手动操作且需要用户操作时才显示处理按钮
                if (status == ThreadStatus.WaitingForManualAction && needsUserAction)
                {
                    return Visibility.Visible;
                }
            }
            return Visibility.Collapsed;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
