using System.Collections.Generic;

namespace AWSAutoRegister.Services
{
    public static class CountryMappingService
    {
        // 国家代码到国家全名的映射表
        private static readonly Dictionary<string, string> CountryMapping = new()
        {
            ["US"] = "United States",
            ["CA"] = "Canada",
            ["MX"] = "Mexico",
            ["GB"] = "United Kingdom",
            ["DE"] = "Germany",
            ["FR"] = "France",
            ["IT"] = "Italy",
            ["ES"] = "Spain",
            ["AU"] = "Australia",
            ["JP"] = "Japan",
            ["KR"] = "South Korea",
            ["CN"] = "China",
            ["IN"] = "India",
            ["BR"] = "Brazil",
			["KR"] = "Korea, Republic of",
            ["AR"] = "Argentina",
            ["CL"] = "Chile",
            ["CO"] = "Colombia",
            ["PE"] = "Peru",
            ["NL"] = "Netherlands",
            ["BE"] = "Belgium",
            ["CH"] = "Switzerland",
            ["AT"] = "Austria",
            ["SE"] = "Sweden",
            ["NO"] = "Norway",
            ["DK"] = "Denmark",
            ["FI"] = "Finland",
            ["PL"] = "Poland",
            ["CZ"] = "Czech Republic",
            ["HU"] = "Hungary",
            ["RO"] = "Romania",
            ["BG"] = "Bulgaria",
            ["HR"] = "Croatia",
            ["SI"] = "Slovenia",
            ["SK"] = "Slovakia",
            ["LT"] = "Lithuania",
            ["LV"] = "Latvia",
            ["EE"] = "Estonia",
            ["IE"] = "Ireland",
            ["PT"] = "Portugal",
            ["GR"] = "Greece",
            ["TR"] = "Turkey",
            ["RU"] = "Russia",
            ["UA"] = "Ukraine",
			["PY"] = "Paraguay",
            ["BY"] = "Belarus",
            ["MD"] = "Moldova",
            ["RS"] = "Serbia",
            //["BA"] = "Bosnia and Herzegovina",
            ["ME"] = "Montenegro",
            ["MK"] = "North Macedonia",
            ["AL"] = "Albania",
            ["XK"] = "Kosovo",
            ["IL"] = "Israel",
            ["SA"] = "Saudi Arabia",
			["AZ"] = "Azerbaijan",
			["KZ"] = "Kazakhstan",
            ["AE"] = "United Arab Emirates",
            ["QA"] = "Qatar",
            ["KW"] = "Kuwait",
            ["BH"] = "Bahrain",
            ["OM"] = "Oman",
            ["JO"] = "Jordan",
            ["LB"] = "Lebanon",
            ["SY"] = "Syria",
            ["IQ"] = "Iraq",
            ["IR"] = "Iran",
            ["AF"] = "Afghanistan",
            ["PK"] = "Pakistan",
            ["BD"] = "Bangladesh",
            ["LK"] = "Sri Lanka",
            ["NP"] = "Nepal",
            ["BT"] = "Bhutan",
            ["MV"] = "Maldives",
            ["TH"] = "Thailand",
            ["VN"] = "Vietnam",
            ["LA"] = "Laos",
            ["KH"] = "Cambodia",
            ["MM"] = "Myanmar",
            ["MY"] = "Malaysia",
			["KG"] = "Kyrgyzstan",
            ["SG"] = "Singapore",
            ["ID"] = "Indonesia",
            ["PH"] = "Philippines",
            ["BN"] = "Brunei",
            ["TL"] = "Timor-Leste",
            ["NZ"] = "New Zealand",
            ["FJ"] = "Fiji",
            ["PG"] = "Papua New Guinea",
            ["SB"] = "Solomon Islands",
            ["VU"] = "Vanuatu",
            //["NC"] = "New Caledonia",
            //["PF"] = "French Polynesia",
            ["WS"] = "Samoa",
            ["TO"] = "Tonga",
            ["KI"] = "Kiribati",
            ["TV"] = "Tuvalu",
            ["NR"] = "Nauru",
            ["PW"] = "Palau",
            ["FM"] = "Micronesia",
            ["MH"] = "Marshall Islands",
            ["ZA"] = "South Africa",
            ["EG"] = "Egypt",
            ["LY"] = "Libya",
            ["TN"] = "Tunisia",
            ["DZ"] = "Algeria",
            ["MA"] = "Morocco",
            ["SD"] = "Sudan",
            ["SS"] = "South Sudan",
            ["ET"] = "Ethiopia",
            ["ER"] = "Eritrea",
            ["DJ"] = "Djibouti",
            ["SO"] = "Somalia",
            ["KE"] = "Kenya",
            ["UG"] = "Uganda",
            ["TZ"] = "Tanzania",
            ["RW"] = "Rwanda",
            ["BI"] = "Burundi",
            //["CD"] = "Democratic Republic of the Congo",
            ["CG"] = "Republic of the Congo",
            //["CF"] = "Central African Republic",
            ["TD"] = "Chad",
            ["CM"] = "Cameroon",
            //["GQ"] = "Equatorial Guinea",
            ["GA"] = "Gabon",
            //["ST"] = "Sao Tome and Principe",
            ["GH"] = "Ghana",
            ["TG"] = "Togo",
            ["BJ"] = "Benin",
            ["NE"] = "Niger",
            ["BF"] = "Burkina Faso",
            ["ML"] = "Mali",
            ["SN"] = "Senegal",
            ["GM"] = "Gambia",
            //["GW"] = "Guinea-Bissau",
            ["GN"] = "Guinea",
            ["SL"] = "Sierra Leone",
            ["LR"] = "Liberia",
            //["CI"] = "Ivory Coast",
            ["MR"] = "Mauritania",
            ["MG"] = "Madagascar",
            ["MU"] = "Mauritius",
            ["SC"] = "Seychelles",
            ["KM"] = "Comoros",
            ["YT"] = "Mayotte",
            ["RE"] = "Reunion",
            ["SH"] = "Saint Helena",
            ["CV"] = "Cape Verde",
            ["ZW"] = "Zimbabwe",
            ["ZM"] = "Zambia",
            ["MW"] = "Malawi",
            ["MZ"] = "Mozambique",
            ["SZ"] = "Eswatini",
            ["LS"] = "Lesotho",
            ["BW"] = "Botswana",
            ["NA"] = "Namibia",
            ["AO"] = "Angola",

            // 补充缺失的国家
            ["AD"] = "Andorra",
            //["AG"] = "Antigua and Barbuda",
            ["AI"] = "Anguilla",
            ["AM"] = "Armenia",
            ["AS"] = "American Samoa",
            ["AW"] = "Aruba",
            ["BB"] = "Barbados",
            ["BM"] = "Bermuda",
            ["BS"] = "Bahamas",
            ["BZ"] = "Belize",
            ["CK"] = "Cook Islands",
            ["CR"] = "Costa Rica",
            ["CU"] = "Cuba",
            ["CW"] = "Curaçao",
            ["CY"] = "Cyprus",
            ["DM"] = "Dominica",
            //["DO"] = "Dominican Republic",
            ["EC"] = "Ecuador",
            //["FK"] = "Falkland Islands",
            ["FO"] = "Faroe Islands",
            ["GD"] = "Grenada",
            ["GE"] = "Georgia",
            ["GF"] = "French Guiana",
            ["GI"] = "Gibraltar",
            ["GL"] = "Greenland",
            ["GU"] = "Guam",
            ["GY"] = "Guyana",
            ["HN"] = "Honduras",
            ["HT"] = "Haiti",
            ["IS"] = "Iceland",
            ["JM"] = "Jamaica",
            //["KN"] = "Saint Kitts and Nevis",
            ["KP"] = "North Korea",
            ["KY"] = "Cayman Islands",
            ["LC"] = "Saint Lucia",
            ["LI"] = "Liechtenstein",
            ["LU"] = "Luxembourg",
            ["MC"] = "Monaco",
            ["ME"] = "Montenegro",
            ["MN"] = "Mongolia",
            ["MO"] = "Macao",
            //["MP"] = "Northern Mariana Islands",
            ["MQ"] = "Martinique",
            ["MS"] = "Montserrat",
            ["MT"] = "Malta",
            ["NI"] = "Nicaragua",
            ["NU"] = "Niue",
            ["PA"] = "Panama",
            //["PM"] = "Saint Pierre and Miquelon",
            ["PR"] = "Puerto Rico",
            ["PS"] = "Palestine",
            ["QA"] = "Qatar",
            ["SM"] = "San Marino",
            ["SR"] = "Suriname",
            ["SV"] = "El Salvador",
            //["TC"] = "Turks and Caicos Islands",
            ["TJ"] = "Tajikistan",
            //["TM"] = "Turkmenistan",
            //["TT"] = "Trinidad and Tobago",
            ["UY"] = "Uruguay",
            ["UZ"] = "Uzbekistan",
            //["VA"] = "Vatican City",
            //["VC"] = "Saint Vincent and the Grenadines",
            ["VE"] = "Venezuela",
            //["VG"] = "British Virgin Islands",
            //["VI"] = "U.S. Virgin Islands",
            //["WF"] = "Wallis and Futuna",
            ["YE"] = "Yemen"
        };

        /// <summary>
        /// 根据国家代码获取国家全名
        /// </summary>
        /// <param name="countryCode">国家代码（如US、CA、MX等）</param>
        /// <returns>国家全名，如果找不到则返回"United States"作为默认值</returns>
        public static string GetCountryName(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return "United States";

            var code = countryCode.ToUpper().Trim();
            return CountryMapping.TryGetValue(code, out var countryName) ? countryName : "United States";
        }

        /// <summary>
        /// 检查国家代码是否存在
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>如果存在返回true，否则返回false</returns>
        public static bool IsValidCountryCode(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return false;

            return CountryMapping.ContainsKey(countryCode.ToUpper().Trim());
        }

        /// <summary>
        /// 获取所有支持的国家代码
        /// </summary>
        /// <returns>所有支持的国家代码列表</returns>
        public static IEnumerable<string> GetAllCountryCodes()
        {
            return CountryMapping.Keys;
        }

        /// <summary>
        /// 获取所有国家代码和名称的映射
        /// </summary>
        /// <returns>国家代码和名称的字典</returns>
        public static Dictionary<string, string> GetAllCountryMappings()
        {
            return new Dictionary<string, string>(CountryMapping);
        }

        /// <summary>
        /// 添加新的国家映射（用于扩展）
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <param name="countryName">国家全名</param>
        public static void AddCountryMapping(string countryCode, string countryName)
        {
            if (!string.IsNullOrEmpty(countryCode) && !string.IsNullOrEmpty(countryName))
            {
                CountryMapping[countryCode.ToUpper().Trim()] = countryName.Trim();
            }
        }
    }
}
