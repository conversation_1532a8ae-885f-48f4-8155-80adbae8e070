using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Net.Http;
using AWSAutoRegister.Services;

namespace AWSAutoRegister.Services
{
    public class UsaPhoneApiService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _apiToken;
        private readonly LogService _logService;

        public UsaPhoneApiService(string baseUrl, string apiToken)
        {
            _baseUrl = baseUrl;
            _apiToken = apiToken;
            _logService = LogService.Instance;

            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("Authorization", _apiToken);
        }

        /// <summary>
        /// 获取手机号码（先购买，再获取详情）- 单个号码
        /// </summary>
        public async Task<(bool Success, UsaPhoneInfo? PhoneInfo, string Message)> GetPhoneNumberAsync()
        {
            try
            {
                _logService.LogInfo("[美国API] 开始购买单个手机号码");

                // 第一步：购买号码
                var buyResult = await BuyPhoneNumberAsync(1);
                if (!buyResult.Success)
                {
                    return (false, null, buyResult.Message);
                }

                var orderNum = buyResult.OrderNum;
                _logService.LogInfo($"[美国API] 购买成功，订单号: {orderNum}");

                // 第二步：获取手机号码详情
                var phoneResult = await GetPhoneNumberListAsync(orderNum);
                if (!phoneResult.Success || phoneResult.PhoneInfos == null || phoneResult.PhoneInfos.Count == 0)
                {
                    return (false, null, phoneResult.Message);
                }

                var phoneInfo = phoneResult.PhoneInfos[0];
                _logService.LogInfo($"[美国API] 获取手机号码成功: {phoneInfo.FullNumber}");
                return (true, phoneInfo, "获取成功");
            }
            catch (Exception ex)
            {
                _logService.LogError($"[美国API] 获取手机号码异常: {ex.Message}");
                return (false, null, $"请求异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量获取手机号码（多线程模式）
        /// </summary>
        public async Task<(bool Success, List<UsaPhoneInfo>? PhoneInfos, string Message)> GetPhoneNumbersAsync(int count)
        {
            try
            {
                _logService.LogInfo($"[美国API] 开始批量购买{count}个手机号码");

                // 第一步：批量购买号码
                var buyResult = await BuyPhoneNumberAsync(count);
                if (!buyResult.Success)
                {
                    return (false, null, buyResult.Message);
                }

                var orderNum = buyResult.OrderNum;
                _logService.LogInfo($"[美国API] 批量购买成功，订单号: {orderNum}");

                // 第二步：获取手机号码列表
                var phoneResult = await GetPhoneNumberListAsync(orderNum);
                if (!phoneResult.Success)
                {
                    return (false, null, phoneResult.Message);
                }

                _logService.LogInfo($"[美国API] 批量获取手机号码成功，共{phoneResult.PhoneInfos?.Count ?? 0}个");
                return (true, phoneResult.PhoneInfos, "批量获取成功");
            }
            catch (Exception ex)
            {
                _logService.LogError($"[美国API] 批量获取手机号码异常: {ex.Message}");
                return (false, null, $"请求异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 购买手机号码（支持批量购买）
        /// </summary>
        private async Task<(bool Success, string OrderNum, string Message)> BuyPhoneNumberAsync(int count)
        {
            try
            {
                var url = $"{_baseUrl}/api/v1/buy/create";
                _logService.LogInfo($"[美国API] 请求购买{count}个手机号码: {url}");

                var requestData = new
                {
                    app_id = 81,
                    type = 1,
                    num = count,
                    expiry = 0
                };

                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logService.LogInfo($"[美国API] 购买响应: {responseContent}");

                    var jsonDoc = JsonDocument.Parse(responseContent);
                    if (jsonDoc.RootElement.TryGetProperty("code", out var codeElement) && codeElement.GetInt32() == 1)
                    {
                        if (jsonDoc.RootElement.TryGetProperty("data", out var dataElement) &&
                            dataElement.TryGetProperty("ordernum", out var orderNumElement))
                        {
                            var orderNum = orderNumElement.GetString();
                            if (!string.IsNullOrEmpty(orderNum))
                            {
                                // 检查api_count是否匹配
                                if (dataElement.TryGetProperty("api_count", out var apiCountElement))
                                {
                                    var apiCount = apiCountElement.GetInt32();
                                    _logService.LogInfo($"[美国API] 购买成功，订单号: {orderNum}，数量: {apiCount}");
                                }
                                return (true, orderNum, "购买成功");
                            }
                        }
                    }

                    _logService.LogError($"[美国API] 购买失败: {responseContent}");
                    return (false, "", responseContent);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logService.LogError($"[美国API] 购买请求失败: {response.StatusCode}, {errorContent}");
                    return (false, "", $"请求失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"[美国API] 购买异常: {ex.Message}");
                return (false, "", $"购买异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取手机号码列表
        /// </summary>
        private async Task<(bool Success, List<UsaPhoneInfo>? PhoneInfos, string Message)> GetPhoneNumberListAsync(string orderNum)
        {
            try
            {
                var url = $"{_baseUrl}/api/v1/order/api";
                _logService.LogInfo($"[美国API] 请求获取手机号码列表: {url}");

                var requestData = new
                {
                    ordernum = orderNum
                };

                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logService.LogInfo($"[美国API] 获取列表响应: {responseContent}");

                    var jsonDoc = JsonDocument.Parse(responseContent);
                    if (jsonDoc.RootElement.TryGetProperty("code", out var codeElement) && codeElement.GetInt32() == 1)
                    {
                        if (jsonDoc.RootElement.TryGetProperty("data", out var dataElement) &&
                            dataElement.TryGetProperty("list", out var listElement))
                        {
                            var phoneInfos = new List<UsaPhoneInfo>();

                            for (int i = 0; i < listElement.GetArrayLength(); i++)
                            {
                                var item = listElement[i];
                                if (item.TryGetProperty("tel", out var telElement) &&
                                    item.TryGetProperty("api", out var apiElement))
                                {
                                    var tel = telElement.GetString();
                                    var api = apiElement.GetString();

                                    if (!string.IsNullOrEmpty(tel) && !string.IsNullOrEmpty(api))
                                    {
                                        // 去掉前面的1，文档要求：填入之前去掉前面的1
                                        var localNumber = tel.StartsWith("1") ? tel.Substring(1) : tel;
                                        var fullNumber = $"+1{localNumber}";

                                        var phoneInfo = new UsaPhoneInfo
                                        {
                                            FullNumber = fullNumber,
                                            LocalNumber = localNumber,
                                            ApiUrl = api,
                                            OrderNum = orderNum
                                        };

                                        phoneInfos.Add(phoneInfo);
                                        _logService.LogInfo($"[美国API] 解析号码: {fullNumber}");
                                    }
                                }
                            }

                            if (phoneInfos.Count > 0)
                            {
                                _logService.LogInfo($"[美国API] 获取号码列表成功，共{phoneInfos.Count}个");
                                return (true, phoneInfos, "获取成功");
                            }
                        }
                    }

                    _logService.LogError($"[美国API] 获取列表失败: {responseContent}");
                    return (false, null, responseContent);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logService.LogError($"[美国API] 获取列表请求失败: {response.StatusCode}, {errorContent}");
                    return (false, null, $"请求失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"[美国API] 获取列表异常: {ex.Message}");
                return (false, null, $"获取列表异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取验证码
        /// </summary>
        public async Task<(bool Success, string? VerificationCode, string Message)> GetVerificationCodeAsync(string apiUrl)
        {
            try
            {
                var url = $"{apiUrl}&format=txt1";
                
                _logService.LogInfo($"[美国API] 请求获取验证码: {url}");

                var response = await _httpClient.GetStringAsync(url);
                
                _logService.LogInfo($"[美国API] 验证码响应: {response}");

                // 提取四位数字验证码：Your Amazon Web Services (AWS) verification code is: 9199
                var match = Regex.Match(response, @"\d{4}");
                if (match.Success)
                {
                    var verificationCode = match.Value;
                    _logService.LogInfo($"[美国API] 提取验证码成功: {verificationCode}");
                    return (true, verificationCode, "获取验证码成功");
                }

                return (false, null, "未找到验证码");
            }
            catch (Exception ex)
            {
                _logService.LogError($"[美国API] 获取验证码异常: {ex.Message}");
                return (false, null, $"获取验证码异常: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    public class UsaPhoneInfo
    {
        public string OrderNum { get; set; } = string.Empty;
        public string FullNumber { get; set; } = string.Empty;
        public string LocalNumber { get; set; } = string.Empty;
        public string ApiUrl { get; set; } = string.Empty;
    }
}
