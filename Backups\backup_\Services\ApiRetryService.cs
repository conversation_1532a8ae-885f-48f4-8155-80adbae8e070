using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 通用API重试服务，为榴莲和千川API提供统一的重试机制
    /// </summary>
    public static class ApiRetryService
    {
        // 统一的重试配置
        public const int MaxRetries = 3;
        public const int RetryDelayMs = 1000;

        /// <summary>
        /// 执行带重试的API调用
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="apiCall">API调用委托</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <param name="servicePrefix">服务前缀（如"榴莲API"、"千川API"）</param>
        /// <returns>API调用结果</returns>
        public static async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> apiCall,
            string operationName,
            string servicePrefix)
        {
            Exception? lastException = null;

            for (int retry = 0; retry <= MaxRetries; retry++)
            {
                try
                {
                    LogService.Instance.LogInfo($"[{servicePrefix}] {operationName}，尝试 {retry + 1}/{MaxRetries + 1}");
                    Console.WriteLine($"[{servicePrefix}] {operationName}，尝试 {retry + 1}/{MaxRetries + 1}");

                    var result = await apiCall();
                    return result;
                }
                catch (HttpRequestException ex) when (ex.Message.Contains("HttpClient.Timeout"))
                {
                    lastException = ex;
                    LogService.Instance.LogError($"[{servicePrefix}] {operationName}超时，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");
                    Console.WriteLine($"[{servicePrefix}] {operationName}超时，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");

                    if (retry < MaxRetries)
                    {
                        LogService.Instance.LogInfo($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        Console.WriteLine($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        await Task.Delay(RetryDelayMs);
                        continue;
                    }
                }
                catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || ex.Message.Contains("timeout"))
                {
                    lastException = ex;
                    LogService.Instance.LogError($"[{servicePrefix}] {operationName}请求超时，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");
                    Console.WriteLine($"[{servicePrefix}] {operationName}请求超时，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");

                    if (retry < MaxRetries)
                    {
                        LogService.Instance.LogInfo($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        Console.WriteLine($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        await Task.Delay(RetryDelayMs);
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    LogService.Instance.LogError($"[{servicePrefix}] {operationName}异常，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");
                    Console.WriteLine($"[{servicePrefix}] {operationName}异常，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");

                    if (retry < MaxRetries)
                    {
                        LogService.Instance.LogInfo($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        Console.WriteLine($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        await Task.Delay(RetryDelayMs);
                        continue;
                    }
                }
            }

            // 如果所有重试都失败，抛出最后的异常
            throw new Exception($"{operationName}失败，已重试{MaxRetries}次", lastException);
        }

        /// <summary>
        /// 执行带重试的API调用（返回元组结果）
        /// </summary>
        /// <typeparam name="T">成功时的数据类型</typeparam>
        /// <param name="apiCall">API调用委托</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <param name="servicePrefix">服务前缀（如"榴莲API"、"千川API"）</param>
        /// <returns>包含成功状态、数据和消息的元组</returns>
        public static async Task<(bool Success, T? Data, string Message)> ExecuteWithRetryTupleAsync<T>(
            Func<Task<(bool Success, T? Data, string Message)>> apiCall,
            string operationName,
            string servicePrefix)
        {
            for (int retry = 0; retry <= MaxRetries; retry++)
            {
                try
                {
                    LogService.Instance.LogInfo($"[{servicePrefix}] {operationName}，尝试 {retry + 1}/{MaxRetries + 1}");
                    Console.WriteLine($"[{servicePrefix}] {operationName}，尝试 {retry + 1}/{MaxRetries + 1}");

                    var result = await apiCall();
                    
                    // 如果API调用成功，直接返回结果
                    if (result.Success)
                    {
                        return result;
                    }

                    // 如果API调用失败，但不是网络异常，也直接返回（比如业务逻辑错误）
                    LogService.Instance.LogWarning($"[{servicePrefix}] {operationName}业务失败: {result.Message}");
                    return result;
                }
                catch (HttpRequestException ex) when (ex.Message.Contains("HttpClient.Timeout"))
                {
                    LogService.Instance.LogError($"[{servicePrefix}] {operationName}超时，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");
                    Console.WriteLine($"[{servicePrefix}] {operationName}超时，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");

                    if (retry < MaxRetries)
                    {
                        LogService.Instance.LogInfo($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        Console.WriteLine($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        await Task.Delay(RetryDelayMs);
                        continue;
                    }

                    return (false, default(T), $"{operationName}超时，已重试{MaxRetries}次: {ex.Message}");
                }
                catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || ex.Message.Contains("timeout"))
                {
                    LogService.Instance.LogError($"[{servicePrefix}] {operationName}请求超时，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");
                    Console.WriteLine($"[{servicePrefix}] {operationName}请求超时，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");

                    if (retry < MaxRetries)
                    {
                        LogService.Instance.LogInfo($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        Console.WriteLine($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        await Task.Delay(RetryDelayMs);
                        continue;
                    }

                    return (false, default(T), $"{operationName}请求超时，已重试{MaxRetries}次: {ex.Message}");
                }
                catch (Exception ex)
                {
                    LogService.Instance.LogError($"[{servicePrefix}] {operationName}异常，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");
                    Console.WriteLine($"[{servicePrefix}] {operationName}异常，尝试 {retry + 1}/{MaxRetries + 1}: {ex.Message}");

                    if (retry < MaxRetries)
                    {
                        LogService.Instance.LogInfo($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        Console.WriteLine($"[{servicePrefix}] {RetryDelayMs}ms后重试{operationName}...");
                        await Task.Delay(RetryDelayMs);
                        continue;
                    }

                    return (false, default(T), $"{operationName}异常，已重试{MaxRetries}次: {ex.Message}");
                }
            }

            // 如果所有重试都失败，返回失败结果
            return (false, default(T), $"{operationName}失败，已达到最大重试次数");
        }
    }
}
