using AWSAutoRegister.Models;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 多线程手机号码管理器 - 批量获取手机号码并分配给各线程
    /// </summary>
    public class MultiThreadPhoneNumberManager
    {
        private readonly PhoneApiService? _phoneApiService;
        private readonly UsaPhoneApiService? _usaPhoneApiService;
        private readonly QianchuanPhoneApiService? _qianchuanPhoneApiService;
        private readonly PhoneProvider _provider;
        private static readonly SemaphoreSlim _batchSemaphore = new SemaphoreSlim(1, 1);
        private static readonly Dictionary<int, PhoneNumberInfo> _threadPhoneNumbers = new Dictionary<int, PhoneNumberInfo>();
        private static readonly Dictionary<int, UsaPhoneInfo> _threadUsaPhoneNumbers = new Dictionary<int, UsaPhoneInfo>();
        private static readonly Dictionary<int, QianchuanPhoneApiService.QianchuanPhoneInfo> _threadQianchuanPhoneNumbers = new Dictionary<int, QianchuanPhoneApiService.QianchuanPhoneInfo>();
        private readonly LogService _logService;

        public MultiThreadPhoneNumberManager(PhoneApiService? phoneApiService, UsaPhoneApiService? usaPhoneApiService, QianchuanPhoneApiService? qianchuanPhoneApiService, PhoneProvider provider)
        {
            _phoneApiService = phoneApiService;
            _usaPhoneApiService = usaPhoneApiService;
            _qianchuanPhoneApiService = qianchuanPhoneApiService;
            _provider = provider;
            _logService = LogService.Instance;
        }

        /// <summary>
        /// 批量获取手机号码并分配给各线程
        /// </summary>
        public async Task<bool> BatchGetPhoneNumbersAsync(int threadCount)
        {
            await _batchSemaphore.WaitAsync();
            try
            {
                _logService.LogInfo($"开始批量获取{threadCount}个手机号码，服务商: {_provider}");

                if (_provider == PhoneProvider.Durian && _phoneApiService != null)
                {
                    // 榴莲API：支持批量获取
                    var result = await _phoneApiService.GetMultiplePhoneNumbersAsync(threadCount);
                    if (result.Success && result.PhoneNumbers != null && result.PhoneNumbers.Count >= threadCount)
                    {
                        // 分配手机号码给各线程
                        for (int i = 0; i < threadCount; i++)
                        {
                            int threadId = i + 1; // 线程ID从1开始
                            var phoneInfo = ConvertToModelsPhoneNumberInfo(result.PhoneNumbers[i]);
                            _threadPhoneNumbers[threadId] = phoneInfo;
                            _logService.LogInfo($"线程{threadId}分配榴莲手机号码: {phoneInfo.FullNumber}");
                        }

                        _logService.LogInfo($"榴莲API批量获取手机号码成功，已分配给{threadCount}个线程");
                        return true;
                    }
                    else
                    {
                        _logService.LogError($"榴莲API批量获取手机号码失败: {result.Message}");
                        return false;
                    }
                }
                else if (_provider == PhoneProvider.Qianchuan && _qianchuanPhoneApiService != null)
                {
                    // 千川API：不支持批量获取，需要逐个获取
                    _logService.LogInfo($"千川API开始逐个获取{threadCount}个手机号码");

                    try
                    {
                        int successCount = 0;
                        for (int i = 0; i < threadCount; i++)
                        {
                            int threadId = i + 1;
                            var result = await _qianchuanPhoneApiService.GetPhoneNumberAsync();
                            if (result.Success && result.PhoneInfo != null)
                            {
                                _threadQianchuanPhoneNumbers[threadId] = result.PhoneInfo;
                                _logService.LogInfo($"线程{threadId}分配千川手机号码: {result.PhoneInfo.FullNumber}");
                                successCount++;
                            }
                            else
                            {
                                _logService.LogError($"线程{threadId}千川手机号码获取失败: {result.Message}");
                                break; // 如果有一个失败，停止继续获取
                            }

                            // 每次获取之间等待1秒，避免API频率限制
                            if (i < threadCount - 1)
                            {
                                await Task.Delay(1000);
                            }
                        }

                        if (successCount == threadCount)
                        {
                            _logService.LogInfo($"千川API逐个获取手机号码成功，共{successCount}个");
                            return true;
                        }
                        else
                        {
                            _logService.LogError($"千川API获取手机号码数量不足，获得{successCount}个，需要{threadCount}个，各线程将使用手动模式");
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.LogError($"千川API逐个获取手机号码异常: {ex.Message}");
                        _logService.LogInfo("逐个获取异常，各线程将使用手动模式");
                        return false;
                    }
                }
                else if (_provider == PhoneProvider.USA && _usaPhoneApiService != null)
                {
                    // 美国API：支持批量获取
                    _logService.LogInfo($"美国API开始批量获取{threadCount}个手机号码");

                    try
                    {
                        var result = await _usaPhoneApiService.GetPhoneNumbersAsync(threadCount);
                        if (result.Success && result.PhoneInfos != null && result.PhoneInfos.Count > 0)
                        {
                            // 分配号码到各个线程
                            int assignedCount = 0;
                            for (int i = 0; i < Math.Min(result.PhoneInfos.Count, threadCount); i++)
                            {
                                int threadId = i + 1;
                                _threadUsaPhoneNumbers[threadId] = result.PhoneInfos[i];
                                _logService.LogInfo($"线程{threadId}分配美国手机号码: {result.PhoneInfos[i].FullNumber}");
                                _logService.LogInfo($"[调试] 美国API字典添加：线程{threadId} -> {result.PhoneInfos[i].FullNumber}");
                                assignedCount++;
                            }

                            // 验证分配结果
                            _logService.LogInfo($"[调试] 美国API分配完成后，字典中的线程数量: {_threadUsaPhoneNumbers.Count}");
                            var assignedThreadIds = string.Join(", ", _threadUsaPhoneNumbers.Keys);
                            _logService.LogInfo($"[调试] 美国API分配完成后，字典中的线程ID: [{assignedThreadIds}]");

                            if (assignedCount == threadCount)
                            {
                                _logService.LogInfo($"美国API批量获取手机号码成功，共{assignedCount}个");
                                return true;
                            }
                            else
                            {
                                _logService.LogError($"美国API获取手机号码数量不足，获得{assignedCount}个，需要{threadCount}个，各线程将使用手动模式");
                                return false;
                            }
                        }
                        else
                        {
                            _logService.LogError($"美国API批量获取手机号码失败: {result.Message}");
                            _logService.LogInfo("批量获取失败，各线程将使用手动模式");
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.LogError($"美国API批量获取手机号码异常: {ex.Message}");
                        _logService.LogInfo("批量获取异常，各线程将使用手动模式");
                        return false;
                    }
                }
                else
                {
                    _logService.LogError($"不支持的手机服务商类型: {_provider}");
                    return false;
                }
            }
            finally
            {
                _batchSemaphore.Release();
            }
        }



        /// <summary>
        /// 获取指定线程的手机号码
        /// </summary>
        public PhoneNumberInfo? GetPhoneNumberForThread(int threadId)
        {
            // 添加详细的调试日志
            _logService.LogInfo($"[调试] 线程{threadId}请求手机号码，当前服务商: {_provider}");
            _logService.LogInfo($"[调试] 榴莲API字典中的线程数量: {_threadPhoneNumbers.Count}");
            _logService.LogInfo($"[调试] 千川API字典中的线程数量: {_threadQianchuanPhoneNumbers.Count}");
            _logService.LogInfo($"[调试] 美国API字典中的线程数量: {_threadUsaPhoneNumbers.Count}");

            if (_provider == PhoneProvider.Durian)
            {
                _logService.LogInfo($"[调试] 检查线程{threadId}是否在榴莲API字典中...");
                if (_threadPhoneNumbers.TryGetValue(threadId, out var phoneInfo))
                {
                    _logService.LogInfo($"[调试] 找到线程{threadId}的榴莲手机号码: {phoneInfo.FullNumber}");
                    _logService.LogInfo($"线程{threadId}获取已分配的榴莲手机号码: {phoneInfo.FullNumber}");
                    return phoneInfo;
                }
                else
                {
                    _logService.LogError($"[调试] 线程{threadId}在榴莲API字典中不存在！");
                }
            }
            else if (_provider == PhoneProvider.Qianchuan)
            {
                _logService.LogInfo($"[调试] 检查线程{threadId}是否在千川API字典中...");
                if (_threadQianchuanPhoneNumbers.TryGetValue(threadId, out var qianchuanPhoneInfo))
                {
                    _logService.LogInfo($"[调试] 找到线程{threadId}的千川手机号码: {qianchuanPhoneInfo.FullNumber}");
                    // 转换为PhoneNumberInfo格式
                    var phoneInfo = new PhoneNumberInfo
                    {
                        FullNumber = qianchuanPhoneInfo.FullNumber,
                        CountryCode = "+43", // 千川固定为奥地利区号
                        LocalNumber = qianchuanPhoneInfo.LocalNumber,
                        AssignedTime = DateTime.Now,
                        AssignedThreadId = threadId,
                        IsUsed = false
                    };
                    _logService.LogInfo($"线程{threadId}获取已分配的千川手机号码: {phoneInfo.FullNumber}");
                    return phoneInfo;
                }
                else
                {
                    _logService.LogError($"[调试] 线程{threadId}在千川API字典中不存在！");
                }
            }
            else if (_provider == PhoneProvider.USA)
            {
                _logService.LogInfo($"[调试] 检查线程{threadId}是否在美国API字典中...");

                // 打印字典中所有的线程ID，用于调试
                var allThreadIds = string.Join(", ", _threadUsaPhoneNumbers.Keys);
                _logService.LogInfo($"[调试] 美国API字典中的所有线程ID: [{allThreadIds}]");

                if (_threadUsaPhoneNumbers.TryGetValue(threadId, out var usaPhoneInfo))
                {
                    _logService.LogInfo($"[调试] 找到线程{threadId}的美国手机号码: {usaPhoneInfo.FullNumber}");
                    // 转换为PhoneNumberInfo格式
                    var phoneInfo = new PhoneNumberInfo
                    {
                        FullNumber = usaPhoneInfo.FullNumber,
                        CountryCode = "1", // 美国区号
                        LocalNumber = usaPhoneInfo.LocalNumber,
                        AssignedTime = DateTime.Now,
                        AssignedThreadId = threadId,
                        IsUsed = false
                    };
                    _logService.LogInfo($"线程{threadId}获取已分配的美国手机号码: {phoneInfo.FullNumber}");
                    return phoneInfo;
                }
                else
                {
                    _logService.LogError($"[调试] 线程{threadId}在美国API字典中不存在！");
                }
            }

            _logService.LogWarning($"线程{threadId}未找到分配的手机号码，服务商: {_provider}");
            return null;
        }

        /// <summary>
        /// 获取指定线程的美国手机号码信息（用于验证码获取）
        /// </summary>
        public UsaPhoneInfo? GetUsaPhoneInfoForThread(int threadId)
        {
            if (_provider == PhoneProvider.USA && _threadUsaPhoneNumbers.TryGetValue(threadId, out var usaPhoneInfo))
            {
                _logService.LogInfo($"线程{threadId}获取美国手机号码信息用于验证码获取");
                return usaPhoneInfo;
            }
            return null;
        }

        /// <summary>
        /// 清理线程手机号码缓存
        /// </summary>
        public static void ClearPhoneNumbers()
        {
            _threadPhoneNumbers.Clear();
            _threadUsaPhoneNumbers.Clear();
            _threadQianchuanPhoneNumbers.Clear();
            LogService.Instance.LogInfo("已清理所有线程的手机号码缓存");
        }

        /// <summary>
        /// 获取所有已分配的手机号码
        /// </summary>
        public Dictionary<int, PhoneNumberInfo> GetAllAssignedPhoneNumbers()
        {
            return new Dictionary<int, PhoneNumberInfo>(_threadPhoneNumbers);
        }

        /// <summary>
        /// 检查指定线程是否有分配的手机号码
        /// </summary>
        public bool HasPhoneNumberForThread(int threadId)
        {
            return _threadPhoneNumbers.ContainsKey(threadId);
        }

        /// <summary>
        /// 移除指定线程的手机号码（用于重新分配）
        /// </summary>
        public void RemovePhoneNumberForThread(int threadId)
        {
            if (_threadPhoneNumbers.Remove(threadId))
            {
                _logService.LogInfo($"已移除线程{threadId}的手机号码分配");
            }
        }

        /// <summary>
        /// 为指定线程重新获取手机号码
        /// </summary>
        public async Task<PhoneNumberInfo?> ReassignPhoneNumberForThread(int threadId)
        {
            try
            {
                _logService.LogInfo($"为线程{threadId}重新获取手机号码，服务商: {_provider}");

                if (_provider == PhoneProvider.Durian && _phoneApiService != null)
                {
                    // 榴莲API
                    var result = await _phoneApiService.GetPhoneNumberAsync();
                    if (result.Success && result.PhoneInfo != null)
                    {
                        var phoneInfo = new PhoneNumberInfo
                        {
                            FullNumber = result.PhoneInfo.FullNumber,
                            CountryCode = result.PhoneInfo.CountryCode,
                            LocalNumber = result.PhoneInfo.LocalNumber,
                            AssignedTime = DateTime.Now,
                            AssignedThreadId = threadId,
                            IsUsed = false
                        };

                        _threadPhoneNumbers[threadId] = phoneInfo;
                        _logService.LogInfo($"线程{threadId}重新分配榴莲手机号码: {phoneInfo.FullNumber}");
                        return phoneInfo;
                    }
                    else
                    {
                        _logService.LogError($"线程{threadId}重新获取榴莲手机号码失败: {result.Message}");
                        return null;
                    }
                }
                else if (_provider == PhoneProvider.Qianchuan && _qianchuanPhoneApiService != null)
                {
                    // 千川API
                    var result = await _qianchuanPhoneApiService.GetPhoneNumberAsync();
                    if (result.Success && result.PhoneInfo != null)
                    {
                        _threadQianchuanPhoneNumbers[threadId] = result.PhoneInfo;

                        var phoneInfo = new PhoneNumberInfo
                        {
                            FullNumber = result.PhoneInfo.FullNumber,
                            CountryCode = "+43",
                            LocalNumber = result.PhoneInfo.LocalNumber,
                            AssignedTime = DateTime.Now,
                            AssignedThreadId = threadId,
                            IsUsed = false
                        };

                        _logService.LogInfo($"线程{threadId}重新分配千川手机号码: {phoneInfo.FullNumber}");
                        return phoneInfo;
                    }
                    else
                    {
                        _logService.LogError($"线程{threadId}重新获取千川手机号码失败: {result.Message}");
                        return null;
                    }
                }
                else if (_provider == PhoneProvider.USA && _usaPhoneApiService != null)
                {
                    // 美国API
                    var result = await _usaPhoneApiService.GetPhoneNumberAsync();
                    if (result.Success && result.PhoneInfo != null)
                    {
                        _threadUsaPhoneNumbers[threadId] = result.PhoneInfo;

                        var phoneInfo = new PhoneNumberInfo
                        {
                            FullNumber = result.PhoneInfo.FullNumber,
                            CountryCode = "1",
                            LocalNumber = result.PhoneInfo.LocalNumber,
                            AssignedTime = DateTime.Now,
                            AssignedThreadId = threadId,
                            IsUsed = false
                        };

                        _logService.LogInfo($"线程{threadId}重新分配美国手机号码: {phoneInfo.FullNumber}");
                        return phoneInfo;
                    }
                    else
                    {
                        _logService.LogError($"线程{threadId}重新获取美国手机号码失败: {result.Message}");
                        return null;
                    }
                }
                else
                {
                    _logService.LogError($"线程{threadId}重新获取手机号码失败: 不支持的服务商类型 {_provider}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"为线程{threadId}重新获取手机号码异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 标记手机号码为已使用
        /// </summary>
        public void MarkPhoneNumberAsUsed(int threadId)
        {
            if (_threadPhoneNumbers.TryGetValue(threadId, out var phoneInfo))
            {
                phoneInfo.IsUsed = true;
                _logService.LogInfo($"线程{threadId}的手机号码已标记为已使用: {phoneInfo.FullNumber}");
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public (int Total, int Used, int Available) GetStatistics()
        {
            var total = _threadPhoneNumbers.Count;
            var used = _threadPhoneNumbers.Values.Count(p => p.IsUsed);
            var available = total - used;

            return (total, used, available);
        }

        /// <summary>
        /// 验证手机号码分配的完整性
        /// </summary>
        public bool ValidatePhoneNumberAssignments(int expectedThreadCount)
        {
            if (_threadPhoneNumbers.Count != expectedThreadCount)
            {
                _logService.LogWarning($"手机号码分配数量不匹配: 期望{expectedThreadCount}个，实际{_threadPhoneNumbers.Count}个");
                return false;
            }

            // 检查线程ID连续性
            for (int i = 1; i <= expectedThreadCount; i++)
            {
                if (!_threadPhoneNumbers.ContainsKey(i))
                {
                    _logService.LogWarning($"缺少线程{i}的手机号码分配");
                    return false;
                }
            }

            _logService.LogInfo($"手机号码分配验证通过，共{expectedThreadCount}个线程");
            return true;
        }

        /// <summary>
        /// 导出手机号码分配信息（用于调试）
        /// </summary>
        public string ExportAssignmentInfo()
        {
            var info = new List<string>();
            info.Add($"手机号码分配信息 - 总计: {_threadPhoneNumbers.Count}");
            info.Add("线程ID | 手机号码 | 分配时间 | 是否已使用");
            info.Add("-------|----------|----------|----------");

            foreach (var kvp in _threadPhoneNumbers.OrderBy(x => x.Key))
            {
                var phoneInfo = kvp.Value;
                info.Add($"{kvp.Key,6} | {phoneInfo.FullNumber,-12} | {phoneInfo.AssignedTime:HH:mm:ss} | {(phoneInfo.IsUsed ? "是" : "否")}");
            }

            return string.Join(Environment.NewLine, info);
        }

        /// <summary>
        /// 清理过期的手机号码分配（超过指定时间未使用）
        /// </summary>
        public int CleanupExpiredAssignments(TimeSpan expireAfter)
        {
            var expiredThreadIds = new List<int>();
            var cutoffTime = DateTime.Now - expireAfter;

            foreach (var kvp in _threadPhoneNumbers)
            {
                if (!kvp.Value.IsUsed && kvp.Value.AssignedTime < cutoffTime)
                {
                    expiredThreadIds.Add(kvp.Key);
                }
            }

            foreach (var threadId in expiredThreadIds)
            {
                var phoneInfo = _threadPhoneNumbers[threadId];
                _threadPhoneNumbers.Remove(threadId);
                _logService.LogInfo($"清理过期的手机号码分配: 线程{threadId}, 号码{phoneInfo.FullNumber}");
            }

            if (expiredThreadIds.Count > 0)
            {
                _logService.LogInfo($"清理完成，共清理{expiredThreadIds.Count}个过期分配");
            }

            return expiredThreadIds.Count;
        }

        /// <summary>
        /// 将 PhoneApiService.PhoneNumberInfo 转换为 Models.PhoneNumberInfo
        /// </summary>
        private Models.PhoneNumberInfo ConvertToModelsPhoneNumberInfo(PhoneApiService.PhoneNumberInfo apiPhoneInfo)
        {
            return new Models.PhoneNumberInfo
            {
                FullNumber = apiPhoneInfo.FullNumber,
                CountryCode = apiPhoneInfo.CountryCode,
                LocalNumber = apiPhoneInfo.LocalNumber
            };
        }
    }
}
