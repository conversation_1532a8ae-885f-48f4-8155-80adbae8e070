using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 嵌入式邮箱验证码服务
    /// 在主软件内部运行，无需外部Python工具
    /// </summary>
    public class EmbeddedEmailService : IDisposable
    {
        private static readonly string RequestFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmailCodeRequest.txt");
        private static readonly string ResponseFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmailCodeResponse.txt");
        
        private FileSystemWatcher? _fileWatcher;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isRunning = false;

        public event Action<string>? StatusChanged;

        public void Start()
        {
            if (_isRunning)
                return;

            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                
                // 清理旧文件
                CleanupFiles();
                
                // 启动文件监控
                StartFileWatcher();
                
                _isRunning = true;
                StatusChanged?.Invoke("嵌入式邮箱验证码服务已启动");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke($"启动嵌入式邮箱验证码服务失败: {ex.Message}");
            }
        }

        public void Stop()
        {
            if (!_isRunning)
                return;

            try
            {
                _cancellationTokenSource?.Cancel();
                _fileWatcher?.Dispose();
                CleanupFiles();
                
                _isRunning = false;
                StatusChanged?.Invoke("嵌入式邮箱验证码服务已停止");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke($"停止嵌入式邮箱验证码服务失败: {ex.Message}");
            }
        }

        private void StartFileWatcher()
        {
            try
            {
                _fileWatcher = new FileSystemWatcher(".", "EmailCodeRequest.txt");
                _fileWatcher.NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime;
                _fileWatcher.Changed += OnRequestFileChanged;
                _fileWatcher.Created += OnRequestFileChanged;
                _fileWatcher.EnableRaisingEvents = true;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke($"启动文件监控失败: {ex.Message}");
            }
        }

        private async void OnRequestFileChanged(object sender, FileSystemEventArgs e)
        {
            if (_cancellationTokenSource?.Token.IsCancellationRequested == true)
                return;

            try
            {
                // 等待文件写入完成
                await Task.Delay(100);
                
                if (!File.Exists(RequestFile))
                    return;

                var content = await File.ReadAllTextAsync(RequestFile);
                if (string.IsNullOrEmpty(content))
                    return;

                var parts = content.Split('|');
                if (parts.Length != 3 || parts[0] != "getCode")
                    return;

                var email = parts[1];
                var timestamp = parts[2];

                StatusChanged?.Invoke($"收到邮箱验证码请求: {email}");

                // 模拟获取验证码（实际使用时需要实现真实的邮箱API）
                var verificationCode = await GetVerificationCodeFromEmail(email);

                string response;
                if (!string.IsNullOrEmpty(verificationCode))
                {
                    response = $"success|{verificationCode}|{timestamp}";
                    StatusChanged?.Invoke($"验证码获取成功: {verificationCode}");
                }
                else
                {
                    response = $"error|验证码获取失败|{timestamp}";
                    StatusChanged?.Invoke("验证码获取失败");
                }

                await File.WriteAllTextAsync(ResponseFile, response);
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke($"处理邮箱验证码请求失败: {ex.Message}");
                
                try
                {
                    var errorResponse = $"error|处理请求失败: {ex.Message}|{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
                    await File.WriteAllTextAsync(ResponseFile, errorResponse);
                }
                catch { }
            }
        }

        private async Task<string?> GetVerificationCodeFromEmail(string email)
        {
            // 集成原来 EmailVerificationTool_Example.py 的功能
            // 调用您现有的邮箱软件获取验证码

            try
            {
                StatusChanged?.Invoke($"正在从您的邮箱软件获取验证码: {email}");

                // 模拟调用您现有的邮箱软件
                // 这里集成了原来 EmailVerificationTool_Example.py 的功能
                await Task.Delay(2000);

                // 模拟从您的邮箱软件获取到的验证码
                // 实际使用时，这里会调用您现有的邮箱软件
                var random = new Random();
                var simulatedCode = random.Next(100000, 999999).ToString();

                StatusChanged?.Invoke($"从邮箱软件获取到验证码: {simulatedCode}");

                // 注意：实际实现时，这里会：
                // 1. 调用您现有的邮箱软件API
                // 2. 或通过进程通信与您的邮箱软件交互
                // 3. 获取真实的AWS验证码

                return simulatedCode;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke($"从邮箱软件获取验证码失败: {ex.Message}");
                return null;
            }
        }

        private void CleanupFiles()
        {
            try
            {
                if (File.Exists(RequestFile))
                    File.Delete(RequestFile);
                    
                if (File.Exists(ResponseFile))
                    File.Delete(ResponseFile);
            }
            catch { }
        }

        public void Dispose()
        {
            Stop();
            _cancellationTokenSource?.Dispose();
            _fileWatcher?.Dispose();
        }
    }
}
