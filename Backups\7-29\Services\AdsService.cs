using Newtonsoft.Json;
using System.Net.Http;
using System.Text;

namespace AWSAutoRegister.Services
{
    public class AdsService
    {
        private readonly HttpClient _httpClient;
        private readonly ConfigService _configService;

        public AdsService()
        {
            _httpClient = new HttpClient();
            _configService = new ConfigService();
        }

        public async Task<string> StartBrowserAsync(string profileId)
        {
            try
            {
                // 检查API Key是否配置
                if (string.IsNullOrEmpty(_configService.AdsApiKey))
                {
                    throw new Exception("API Key未配置。请在config.json文件中设置正确的AdsApiKey。");
                }

                // 首先测试API连接
                await TestApiConnectionAsync();

                var url = $"{_configService.AdsApiUrl}/api/v1/browser/start?user_id={profileId}&api_key={_configService.AdsApiKey}";

                var response = await _httpClient.GetAsync(url);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<dynamic>(responseContent);
                    var wsEndpoint = result?.data?.ws?.puppeteer?.ToString();

                    if (string.IsNullOrEmpty(wsEndpoint))
                    {
                        throw new Exception($"无法获取浏览器连接地址。响应: {responseContent}");
                    }

                    return wsEndpoint!;
                }
                else
                {
                    throw new Exception($"启动浏览器失败 (HTTP {response.StatusCode}): {responseContent}");
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"无法连接到Adspower API ({_configService.AdsApiUrl})。请确保Adspower软件已启动。详细错误: {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new Exception($"连接Adspower失败: {ex.Message}");
            }
        }

        private async Task TestApiConnectionAsync()
        {
            try
            {
                // 使用一个简单的API端点进行测试，比如获取用户列表
                var testUrl = $"{_configService.AdsApiUrl}/api/v1/user/list";
                var response = await _httpClient.GetAsync(testUrl);
                // 不管返回什么状态码，只要能连接就行
            }
            catch (HttpRequestException)
            {
                throw new Exception($"无法连接到Adspower API服务器 ({_configService.AdsApiUrl})。请检查:\n1. Adspower软件是否已启动\n2. API服务是否正常运行\n3. 防火墙是否阻止连接");
            }
        }

        public async Task<bool> StopBrowserAsync(string profileId)
        {
            try
            {
                // 检查API Key是否配置
                if (string.IsNullOrEmpty(_configService.AdsApiKey))
                {
                    return false; // 静默失败，因为这通常在清理时调用
                }

                var url = $"{_configService.AdsApiUrl}/api/v1/browser/stop?user_id={profileId}&api_key={_configService.AdsApiKey}";

                var response = await _httpClient.GetAsync(url);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
