<UserControl x:Class="AWSAutoRegister.Controls.SearchableCountryComboBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="35" d:DesignWidth="300">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 搜索输入框 -->
        <Border Grid.Row="0" 
                BorderBrush="#CBD5E0" 
                BorderThickness="1" 
                CornerRadius="6"
                Background="White">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Name="SearchTextBox"
                         Grid.Column="0"
                         Padding="8,6"
                         BorderThickness="0"
                         Background="Transparent"
                         FontSize="13"
                         TextChanged="SearchTextBox_TextChanged"
                         GotFocus="SearchTextBox_GotFocus"
                         LostFocus="SearchTextBox_LostFocus"/>
                
                <TextBlock Name="PlaceholderText"
                           Grid.Column="0"
                           Text="搜索国家名称、代码或区号..."
                           Foreground="#A0AEC0"
                           FontSize="13"
                           Margin="10,8"
                           IsHitTestVisible="False"
                           Visibility="Visible"/>
                
                <Button Name="DropDownButton"
                        Grid.Column="1"
                        Width="24"
                        Height="24"
                        Margin="4"
                        Background="Transparent"
                        BorderThickness="0"
                        Click="DropDownButton_Click"
                        Cursor="Hand">
                    <Path Data="M7,10L12,15L17,10H7Z" 
                          Fill="#4A5568" 
                          HorizontalAlignment="Center" 
                          VerticalAlignment="Center"/>
                </Button>
            </Grid>
        </Border>
        
        <!-- 下拉列表 -->
        <Border Name="DropDownBorder"
                Grid.Row="1"
                BorderBrush="#CBD5E0"
                BorderThickness="1,0,1,1"
                CornerRadius="0,0,6,6"
                Background="White"
                MaxHeight="200"
                Visibility="Collapsed"
                Panel.ZIndex="1000">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <ListBox Name="CountryListBox"
                         BorderThickness="0"
                         Background="Transparent"
                         SelectionChanged="CountryListBox_SelectionChanged">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="8,4">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" 
                                           Text="{Binding Name}" 
                                           FontSize="13"
                                           VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="1" 
                                           Text="{Binding Code}" 
                                           FontSize="11"
                                           Foreground="#718096"
                                           VerticalAlignment="Center"
                                           Margin="8,0,0,0"/>
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem">
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Margin" Value="0"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#F7FAFC"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#EDF2F7"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </ListBox.ItemContainerStyle>
                </ListBox>
            </ScrollViewer>
        </Border>
    </Grid>
</UserControl>
