﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C3027320871917014D21C04B90669F0D9111E062"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AWSAutoRegister {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 332 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectFileButton;
        
        #line default
        #line hidden
        
        
        #line 347 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FilePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 357 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadDataButton;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton AdsPowerModeRadio;
        
        #line default
        #line hidden
        
        
        #line 401 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LocalChromeNormalModeRadio;
        
        #line default
        #line hidden
        
        
        #line 406 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LocalChromeIncognitoModeRadio;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThreadCountComboBox;
        
        #line default
        #line hidden
        
        
        #line 446 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ThreadModeHint;
        
        #line default
        #line hidden
        
        
        #line 454 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenMultiThreadWindowBtn;
        
        #line default
        #line hidden
        
        
        #line 465 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AdsPowerConfigGrid;
        
        #line default
        #line hidden
        
        
        #line 487 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProfileIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 496 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestConnectionButton;
        
        #line default
        #line hidden
        
        
        #line 507 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LocalChromeConfigGrid;
        
        #line default
        #line hidden
        
        
        #line 520 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestLocalChromeButton;
        
        #line default
        #line hidden
        
        
        #line 571 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PhoneDurianModeRadio;
        
        #line default
        #line hidden
        
        
        #line 576 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PhoneQianchuanModeRadio;
        
        #line default
        #line hidden
        
        
        #line 581 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PhoneUsaModeRadio;
        
        #line default
        #line hidden
        
        
        #line 586 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PhoneManualModeRadio;
        
        #line default
        #line hidden
        
        
        #line 599 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse PhoneModeIndicator;
        
        #line default
        #line hidden
        
        
        #line 604 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PhoneModeText;
        
        #line default
        #line hidden
        
        
        #line 614 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PhoneApiConfigGrid;
        
        #line default
        #line hidden
        
        
        #line 636 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneApiNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 657 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneApiKeyTextBox;
        
        #line default
        #line hidden
        
        
        #line 667 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PhoneApiConfigGrid2;
        
        #line default
        #line hidden
        
        
        #line 689 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneApiProjectIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 707 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CountrySearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 722 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SearchResultsPopup;
        
        #line default
        #line hidden
        
        
        #line 742 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SearchResultsListBox;
        
        #line default
        #line hidden
        
        
        #line 780 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PhoneApiConfigGrid3;
        
        #line default
        #line hidden
        
        
        #line 788 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestPhoneApiButton;
        
        #line default
        #line hidden
        
        
        #line 796 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SavePhoneApiButton;
        
        #line default
        #line hidden
        
        
        #line 839 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton EmailGoogleRadio;
        
        #line default
        #line hidden
        
        
        #line 844 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton EmailMicrosoftRadio;
        
        #line default
        #line hidden
        
        
        #line 849 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton EmailManualModeRadio;
        
        #line default
        #line hidden
        
        
        #line 869 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmailModeDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 914 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CaptchaYesModeRadio;
        
        #line default
        #line hidden
        
        
        #line 921 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CaptchaCloudModeRadio;
        
        #line default
        #line hidden
        
        
        #line 928 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CaptchaManualModeRadio;
        
        #line default
        #line hidden
        
        
        #line 950 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CaptchaModeDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 985 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartButton;
        
        #line default
        #line hidden
        
        
        #line 996 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ContinueButton;
        
        #line default
        #line hidden
        
        
        #line 1007 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PauseButton;
        
        #line default
        #line hidden
        
        
        #line 1018 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopButton;
        
        #line default
        #line hidden
        
        
        #line 1068 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1092 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BrowserModeIndicator;
        
        #line default
        #line hidden
        
        
        #line 1102 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BrowserModeText;
        
        #line default
        #line hidden
        
        
        #line 1171 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnprocessedCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1191 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1211 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcessingCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1231 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1251 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FailedCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1269 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AWS;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 15 "..\..\..\..\MainWindow.xaml"
            ((AWSAutoRegister.MainWindow)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.MainWindow_Closing);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SelectFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 336 "..\..\..\..\MainWindow.xaml"
            this.SelectFileButton.Click += new System.Windows.RoutedEventHandler(this.SelectFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.FilePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.LoadDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 360 "..\..\..\..\MainWindow.xaml"
            this.LoadDataButton.Click += new System.Windows.RoutedEventHandler(this.LoadDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AdsPowerModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 400 "..\..\..\..\MainWindow.xaml"
            this.AdsPowerModeRadio.Checked += new System.Windows.RoutedEventHandler(this.BrowserModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 6:
            this.LocalChromeNormalModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 405 "..\..\..\..\MainWindow.xaml"
            this.LocalChromeNormalModeRadio.Checked += new System.Windows.RoutedEventHandler(this.BrowserModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.LocalChromeIncognitoModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 409 "..\..\..\..\MainWindow.xaml"
            this.LocalChromeIncognitoModeRadio.Checked += new System.Windows.RoutedEventHandler(this.BrowserModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ThreadCountComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 435 "..\..\..\..\MainWindow.xaml"
            this.ThreadCountComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ThreadCountComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ThreadModeHint = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.OpenMultiThreadWindowBtn = ((System.Windows.Controls.Button)(target));
            
            #line 461 "..\..\..\..\MainWindow.xaml"
            this.OpenMultiThreadWindowBtn.Click += new System.Windows.RoutedEventHandler(this.OpenMultiThreadWindowBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.AdsPowerConfigGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.ProfileIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.TestConnectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 498 "..\..\..\..\MainWindow.xaml"
            this.TestConnectionButton.Click += new System.Windows.RoutedEventHandler(this.TestConnectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.LocalChromeConfigGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.TestLocalChromeButton = ((System.Windows.Controls.Button)(target));
            
            #line 522 "..\..\..\..\MainWindow.xaml"
            this.TestLocalChromeButton.Click += new System.Windows.RoutedEventHandler(this.TestLocalChromeButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.PhoneDurianModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 575 "..\..\..\..\MainWindow.xaml"
            this.PhoneDurianModeRadio.Checked += new System.Windows.RoutedEventHandler(this.PhoneModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PhoneQianchuanModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 580 "..\..\..\..\MainWindow.xaml"
            this.PhoneQianchuanModeRadio.Checked += new System.Windows.RoutedEventHandler(this.PhoneModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 18:
            this.PhoneUsaModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 585 "..\..\..\..\MainWindow.xaml"
            this.PhoneUsaModeRadio.Checked += new System.Windows.RoutedEventHandler(this.PhoneModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 19:
            this.PhoneManualModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 589 "..\..\..\..\MainWindow.xaml"
            this.PhoneManualModeRadio.Checked += new System.Windows.RoutedEventHandler(this.PhoneModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 20:
            this.PhoneModeIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 21:
            this.PhoneModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.PhoneApiConfigGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 23:
            this.PhoneApiNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.PhoneApiKeyTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.PhoneApiConfigGrid2 = ((System.Windows.Controls.Grid)(target));
            return;
            case 26:
            this.PhoneApiProjectIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 27:
            this.CountrySearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 716 "..\..\..\..\MainWindow.xaml"
            this.CountrySearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.CountrySearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 717 "..\..\..\..\MainWindow.xaml"
            this.CountrySearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.CountrySearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 718 "..\..\..\..\MainWindow.xaml"
            this.CountrySearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CountrySearchTextBox_TextChanged);
            
            #line default
            #line hidden
            
            #line 719 "..\..\..\..\MainWindow.xaml"
            this.CountrySearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.CountrySearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 28:
            this.SearchResultsPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 29:
            this.SearchResultsListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 745 "..\..\..\..\MainWindow.xaml"
            this.SearchResultsListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SearchResultsListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 30:
            this.PhoneApiConfigGrid3 = ((System.Windows.Controls.Grid)(target));
            return;
            case 31:
            this.TestPhoneApiButton = ((System.Windows.Controls.Button)(target));
            
            #line 790 "..\..\..\..\MainWindow.xaml"
            this.TestPhoneApiButton.Click += new System.Windows.RoutedEventHandler(this.TestPhoneApiButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.SavePhoneApiButton = ((System.Windows.Controls.Button)(target));
            
            #line 798 "..\..\..\..\MainWindow.xaml"
            this.SavePhoneApiButton.Click += new System.Windows.RoutedEventHandler(this.SavePhoneApiButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.EmailGoogleRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 843 "..\..\..\..\MainWindow.xaml"
            this.EmailGoogleRadio.Checked += new System.Windows.RoutedEventHandler(this.EmailModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 34:
            this.EmailMicrosoftRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 848 "..\..\..\..\MainWindow.xaml"
            this.EmailMicrosoftRadio.Checked += new System.Windows.RoutedEventHandler(this.EmailModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 35:
            this.EmailManualModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 852 "..\..\..\..\MainWindow.xaml"
            this.EmailManualModeRadio.Checked += new System.Windows.RoutedEventHandler(this.EmailModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 36:
            this.EmailModeDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.CaptchaYesModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 920 "..\..\..\..\MainWindow.xaml"
            this.CaptchaYesModeRadio.Checked += new System.Windows.RoutedEventHandler(this.CaptchaYesModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 38:
            this.CaptchaCloudModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 927 "..\..\..\..\MainWindow.xaml"
            this.CaptchaCloudModeRadio.Checked += new System.Windows.RoutedEventHandler(this.CaptchaCloudModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 39:
            this.CaptchaManualModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 933 "..\..\..\..\MainWindow.xaml"
            this.CaptchaManualModeRadio.Checked += new System.Windows.RoutedEventHandler(this.CaptchaManualModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 40:
            this.CaptchaModeDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.StartButton = ((System.Windows.Controls.Button)(target));
            
            #line 991 "..\..\..\..\MainWindow.xaml"
            this.StartButton.Click += new System.Windows.RoutedEventHandler(this.StartButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.ContinueButton = ((System.Windows.Controls.Button)(target));
            
            #line 1002 "..\..\..\..\MainWindow.xaml"
            this.ContinueButton.Click += new System.Windows.RoutedEventHandler(this.ContinueButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.PauseButton = ((System.Windows.Controls.Button)(target));
            
            #line 1013 "..\..\..\..\MainWindow.xaml"
            this.PauseButton.Click += new System.Windows.RoutedEventHandler(this.PauseButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.StopButton = ((System.Windows.Controls.Button)(target));
            
            #line 1024 "..\..\..\..\MainWindow.xaml"
            this.StopButton.Click += new System.Windows.RoutedEventHandler(this.StopButton_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 46:
            this.BrowserModeIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 47:
            this.BrowserModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.UnprocessedCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.PendingCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.ProcessingCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.CompletedCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.FailedCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 53:
            this.DataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

