using System.IO;
using System.Text.Json;

namespace AWSAutoRegister.Services
{
    public class ConfigService
    {
        private const string ConfigFileName = "config.json";
        private ApiConfig _config = new ApiConfig();

        public ConfigService()
        {
            LoadConfig();
        }

        public string AdsApiUrl => _config.AdsApiUrl;
        public string AdsApiKey => _config.AdsApiKey;
        public BrowserMode BrowserMode => _config.BrowserMode;
        public PhoneApiConfig PhoneApi => _config.PhoneApi;
        public EmailVerificationConfig EmailVerification => _config.EmailVerification;
        public CaptchaRecognitionConfig CaptchaRecognition => _config.CaptchaRecognition;

        private void LoadConfig()
        {
            try
            {
                if (File.Exists(ConfigFileName))
                {
                    var json = File.ReadAllText(ConfigFileName);
                    _config = JsonSerializer.Deserialize<ApiConfig>(json) ?? new ApiConfig();
                }
                else
                {
                    _config = new ApiConfig();
                    SaveConfig();
                }
            }
            catch
            {
                _config = new ApiConfig();
            }
        }

        public void UpdateConfig(string apiUrl, string apiKey)
        {
            _config.AdsApiUrl = apiUrl;
            _config.AdsApiKey = apiKey;
            SaveConfig();
        }

        public void UpdateBrowserMode(BrowserMode browserMode)
        {
            _config.BrowserMode = browserMode;
            System.Diagnostics.Debug.WriteLine($"UpdateBrowserMode: 设置为 {browserMode} (值: {(int)browserMode})");
            SaveConfig();
            System.Diagnostics.Debug.WriteLine($"UpdateBrowserMode: 配置已保存");
        }

        public void UpdatePhoneApiConfig(string name, string apiKey, string projectId, string countryCode, bool autoMode)
        {
            _config.PhoneApi.Name = name;
            _config.PhoneApi.ApiKey = apiKey;
            _config.PhoneApi.ProjectId = projectId;
            _config.PhoneApi.CountryCode = countryCode;
            _config.PhoneApi.AutoMode = autoMode;
            // 注意：不覆盖Provider设置，Provider由单独的方法管理
            SaveConfig();
        }

        public void UpdatePhoneApiProvider(PhoneProvider provider)
        {
            _config.PhoneApi.Provider = provider;
            // 根据服务商设置AutoMode
            _config.PhoneApi.AutoMode = provider != PhoneProvider.Manual;
            SaveConfig();
        }

        public void UpdateEmailVerificationConfig(bool autoMode)
        {
            _config.EmailVerification.AutoMode = autoMode;
            SaveConfig();
        }

        public void UpdateEmailVerificationProvider(string provider)
        {
            _config.EmailVerification.Provider = provider;
            SaveConfig();
        }

        public void UpdateCaptchaRecognitionConfig(bool autoMode)
        {
            _config.CaptchaRecognition.AutoMode = autoMode;
            SaveConfig();
        }

        public void UpdateCaptchaRecognitionProvider(string provider)
        {
            _config.CaptchaRecognition.Provider = provider;
            SaveConfig();
        }

        public void RefreshConfig()
        {
            LoadConfig();
        }

        private void SaveConfig()
        {
            try
            {
                var json = JsonSerializer.Serialize(_config, new JsonSerializerOptions { WriteIndented = true });
                System.Diagnostics.Debug.WriteLine($"SaveConfig: 保存到 {ConfigFileName}");
                System.Diagnostics.Debug.WriteLine($"SaveConfig: BrowserMode = {_config.BrowserMode} (值: {(int)_config.BrowserMode})");
                File.WriteAllText(ConfigFileName, json);
                System.Diagnostics.Debug.WriteLine($"SaveConfig: 文件已写入");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SaveConfig: 保存失败 - {ex.Message}");
            }
        }
    }

    public enum BrowserMode
    {
        AdsPower,
        LocalChromeIncognito,  // 保持原来的LocalChrome对应值1
        LocalChromeNormal      // 新增的默认Chrome模式为值2
    }

    public class ApiConfig
    {
        public string AdsApiUrl { get; set; } = "http://127.0.0.1:50325";
        public string AdsApiKey { get; set; } = "";
        public BrowserMode BrowserMode { get; set; } = BrowserMode.AdsPower;
        public PhoneApiConfig PhoneApi { get; set; } = new PhoneApiConfig();
        public EmailVerificationConfig EmailVerification { get; set; } = new EmailVerificationConfig();
        public CaptchaRecognitionConfig CaptchaRecognition { get; set; } = new CaptchaRecognitionConfig();
    }

    public enum PhoneProvider
    {
        Durian = 0,      // 榴莲
        Qianchuan = 1,   // 千川
        USA = 2,         // 美国
        Manual = 3       // 手动模式
    }

    public class PhoneApiConfig
    {
        public string Name { get; set; } = "";
        public string ApiKey { get; set; } = "";
        public string ProjectId { get; set; } = "0209";
        public string CountryCode { get; set; } = "th";
        public bool AutoMode { get; set; } = true;
        public string BaseUrl { get; set; } = "https://api.durianrcs.com/out/ext_api";
        public PhoneProvider Provider { get; set; } = PhoneProvider.Durian;
        public string UsaApiToken { get; set; } = "A28AFF657DC03E5B4E165400E3F1";
        public string UsaBaseUrl { get; set; } = "https://www.api21k.com";

        // 千川API配置
        public string QianchuanToken { get; set; } = "";
        public string QianchuanChannelId { get; set; } = "7";
        public string QianchuanBaseUrl { get; set; } = "https://api.qc86.shop";
    }

    public class EmailVerificationConfig
    {
        public bool AutoMode { get; set; } = true;
        public string Provider { get; set; } = "Google"; // 默认选择谷歌
    }

    public class CaptchaRecognitionConfig
    {
        public bool AutoMode { get; set; } = true;
        public string Provider { get; set; } = "YesCaptcha"; // "YesCaptcha" 或 "CloudCaptcha"
        public string Token { get; set; } = "4ID0zhziE7WGreKTWv7HhNLk-y8WIEnBR3lsK_dw8gY"; // 云打码Token
        public string YesClientKey { get; set; } = ""; // Yes打码ClientKey
        public int MaxRetries { get; set; } = 3;
        public string Type { get; set; } = "10103";
    }
}
