# 批量替换AutomationService.cs中的StatusChanged调用为UpdateStatus调用
$filePath = "Services\AutomationService.cs"

if (Test-Path $filePath) {
    Write-Host "正在处理 $filePath ..."

    # 读取文件内容
    $content = Get-Content $filePath -Raw -Encoding UTF8

    # 替换StatusChanged?.Invoke(为UpdateStatus(
    $content = $content -replace 'StatusChanged\?\?\.Invoke\(', 'UpdateStatus('

    # 保存文件
    Set-Content $filePath -Value $content -Encoding UTF8

    Write-Host "替换完成！"
} else {
    Write-Host "文件不存在: $filePath"
}
