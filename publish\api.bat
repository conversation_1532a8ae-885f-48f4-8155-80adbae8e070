@echo off
chcp 65001
echo 正在配置图形验证码API直连路由...
title AWS图形验证码API网络配置工具
color 0A

:main
cls
echo.
echo ================================================================
echo                AWS图形验证码API网络配置工具
echo ================================================================
echo  API域名: api.jfbym.com
echo  API地址: 47.109.179.205
echo ================================================================
echo
echo 请选择操作：
echo [1] 配置图形验证码API使用本地网络
echo [2] 撤销图形验证码API使用本地网络  
echo [3] 测试图形验证码API网络连接
echo [4] 退出
echo
set /p choice=请输入选项 (1-4): 

if "%choice%"=="1" goto config
if "%choice%"=="2" goto remove
if "%choice%"=="3" goto test
if "%choice%"=="4" goto exit
echo 无效选项，请重新选择！
pause
goto main

:config
cls
echo 正在配置图形验证码API直连路由...
route delete 47.109.179.205 >nul 2>&1
route add 47.109.179.205 mask 255.255.255.255 0.0.0.0 metric 1 -p
if %errorlevel% == 0 (
    echo ✅ 路由配置成功！api.jfbym.com 将直连访问
) else (
    echo ❌ 路由配置失败，请以管理员身份运行
)
pause
goto main

:remove
cls
echo 正在撤销图形验证码API直连路由...
route delete 47.109.179.205
if %errorlevel% == 0 (
    echo ✅ 路由撤销成功！
) else (
    echo ❌ 路由撤销失败！
)
pause
goto main

:test
cls
echo 正在测试图形验证码API网络连接...
ping -n 3 47.109.179.205
tracert -h 3 47.109.179.205
pause
goto main

:exit
echo 再见！
timeout /t 2 >nul
exit