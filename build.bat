@echo off
echo ========================================
echo AWS Auto Register Tool - Build Script
echo ========================================
echo.

echo [1/3] Checking .NET SDK...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET SDK not found!
    pause
    exit /b 1
)
echo SUCCESS: .NET SDK found

echo.
echo [2/3] Cleaning and restoring...
dotnet clean AWSAutoRegister.csproj >nul 2>&1
dotnet restore AWSAutoRegister.csproj --verbosity quiet
if errorlevel 1 (
    echo ERROR: Failed to restore dependencies!
    pause
    exit /b 1
)
echo SUCCESS: Dependencies restored

echo.
echo [3/3] Building and publishing...

REM Backup YesClientKey value
if exist "publish\config.json" (
    echo Backing up YesClientKey...
    powershell -Command "try { $config = Get-Content 'publish\config.json' -Raw | ConvertFrom-Json; Write-Host 'Current YesClientKey:' $config.YesClientKey; if ($config.PSObject.Properties.Name -contains 'YesClientKey' -and $config.YesClientKey -ne '') { $config.YesClientKey | Out-File 'yesclientkey_backup.txt' -Encoding UTF8 -NoNewline; Write-Host 'Backup created successfully' } else { Write-Host 'No YesClientKey to backup' } } catch { Write-Host 'Error during backup:' $_.Exception.Message }"
)

dotnet publish AWSAutoRegister.csproj -c Release -r win-x64 --self-contained true -p:PublishSingleFile=false -o publish --verbosity quiet
if errorlevel 1 (
    echo ERROR: Build failed!
    if exist "yesclientkey_backup.txt" del "yesclientkey_backup.txt" >nul 2>&1
    pause
    exit /b 1
)

REM Restore YesClientKey value
if exist "yesclientkey_backup.txt" (
    echo Restoring YesClientKey...
    powershell -Command "try { $key = Get-Content 'yesclientkey_backup.txt' -Raw; Write-Host 'Backed up key:' $key; if ($key -and $key.Trim() -ne '') { $config = Get-Content 'publish\config.json' -Raw | ConvertFrom-Json; $config.YesClientKey = $key.Trim(); $config | ConvertTo-Json -Depth 10 | Set-Content 'publish\config.json' -Encoding UTF8; Write-Host 'Key restored successfully' } else { Write-Host 'Empty backup key' } } catch { Write-Host 'Error during restore:' $_.Exception.Message }"
    del "yesclientkey_backup.txt" >nul 2>&1
    echo SUCCESS: YesClientKey value preserved
) else (
    echo INFO: No YesClientKey to preserve
)

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo File location: publish\AWS.exe
echo Build time: %date% %time%
echo ========================================
echo.
pause
