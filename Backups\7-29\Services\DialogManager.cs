using System.Windows;
using AWSAutoRegister.Models;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 对话框管理器 - 统一处理单线程和多线程模式下的弹窗逻辑
    /// </summary>
    public static class DialogManager
    {
        private static MultiThreadManager? _multiThreadManager;
        private static readonly LogService _logService = LogService.Instance;

        /// <summary>
        /// 设置多线程管理器实例
        /// </summary>
        public static void SetMultiThreadManager(MultiThreadManager multiThreadManager)
        {
            _multiThreadManager = multiThreadManager;
        }

        /// <summary>
        /// 显示对话框（根据线程ID自动选择处理方式）
        /// </summary>
        /// <param name="threadId">线程ID，0表示单线程模式</param>
        /// <param name="title">对话框标题</param>
        /// <param name="message">对话框消息</param>
        /// <param name="isManualMode">是否为手动模式</param>
        /// <param name="dialogType">对话框类型</param>
        public static void ShowDialog(int threadId, string title, string message, bool isManualMode = true, DialogType dialogType = DialogType.Information)
        {
            try
            {
                if (threadId == 0) // 单线程模式
                {
                    ShowSingleThreadDialog(title, message, isManualMode, dialogType);
                }
                else // 多线程模式
                {
                    ShowMultiThreadDialog(threadId, title, message, isManualMode, dialogType);
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"显示对话框失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示确认对话框（仅单线程模式支持）
        /// </summary>
        /// <param name="threadId">线程ID，0表示单线程模式</param>
        /// <param name="title">对话框标题</param>
        /// <param name="message">对话框消息</param>
        /// <param name="defaultResult">默认结果（多线程模式使用）</param>
        /// <returns>用户选择结果</returns>
        public static MessageBoxResult ShowConfirmDialog(int threadId, string title, string message, MessageBoxResult defaultResult = MessageBoxResult.No)
        {
            try
            {
                if (threadId == 0) // 单线程模式
                {
                    return Application.Current.Dispatcher.Invoke(() =>
                    {
                        _logService.LogInfo($"[单线程] 显示确认对话框: {title} - {message}");
                        return MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
                    });
                }
                else // 多线程模式
                {
                    // 多线程模式不显示确认对话框，直接返回默认结果
                    _logService.LogInfo($"[线程{threadId}] 多线程模式跳过确认对话框，使用默认结果: {defaultResult}");
                    ShowMultiThreadDialog(threadId, title, $"{message} (自动选择: {defaultResult})", false, DialogType.Warning);
                    return defaultResult;
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"显示确认对话框失败: {ex.Message}");
                return defaultResult;
            }
        }

        /// <summary>
        /// 显示错误对话框（所有模式都显示）
        /// </summary>
        /// <param name="threadId">线程ID</param>
        /// <param name="title">错误标题</param>
        /// <param name="message">错误消息</param>
        public static void ShowErrorDialog(int threadId, string title, string message)
        {
            ShowDialog(threadId, title, message, true, DialogType.Error);
        }

        /// <summary>
        /// 显示成功对话框
        /// </summary>
        /// <param name="threadId">线程ID</param>
        /// <param name="title">成功标题</param>
        /// <param name="message">成功消息</param>
        public static void ShowSuccessDialog(int threadId, string title, string message)
        {
            ShowDialog(threadId, title, message, false, DialogType.Success);
        }

        /// <summary>
        /// 显示警告对话框
        /// </summary>
        /// <param name="threadId">线程ID</param>
        /// <param name="title">警告标题</param>
        /// <param name="message">警告消息</param>
        public static void ShowWarningDialog(int threadId, string title, string message)
        {
            ShowDialog(threadId, title, message, true, DialogType.Warning);
        }

        /// <summary>
        /// 单线程模式对话框处理
        /// </summary>
        private static void ShowSingleThreadDialog(string title, string message, bool isManualMode, DialogType dialogType)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    _logService.LogInfo($"[单线程] 显示对话框: {title} - {message} (手动模式: {isManualMode})");

                    // 注册失败的弹窗总是显示，不受状态限制
                    bool isFailureDialog = title.Contains("注册失败") || title.Contains("失败") || dialogType == DialogType.Error;

                    // 只有手动模式或失败弹窗才显示窗口和弹窗
                    if (isManualMode || isFailureDialog)
                    {
                        // 获取主窗口并置于前台
                        var mainWindow = Application.Current.MainWindow;
                        if (mainWindow != null)
                        {
                            mainWindow.WindowState = WindowState.Normal;
                            mainWindow.Activate();
                            mainWindow.Topmost = true;
                            mainWindow.Topmost = false;
                        }

                        // 根据对话框类型选择图标
                        var icon = dialogType switch
                        {
                            DialogType.Error => MessageBoxImage.Error,
                            DialogType.Warning => MessageBoxImage.Warning,
                            DialogType.Success => MessageBoxImage.Information,
                            DialogType.Question => MessageBoxImage.Question,
                            _ => MessageBoxImage.Information
                        };

                        MessageBox.Show(mainWindow, message, title, MessageBoxButton.OK, icon);
                    }
                    // 自动模式不显示弹窗，不将窗口置于前台
                }
                catch (Exception ex)
                {
                    _logService.LogError($"[单线程] 显示对话框异常: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 多线程模式对话框处理
        /// </summary>
        private static void ShowMultiThreadDialog(int threadId, string title, string message, bool isManualMode, DialogType dialogType)
        {
            try
            {
                _logService.LogInfo($"[线程{threadId}] 多线程模式状态更新: {title} - {message} (需要用户操作: {isManualMode})");

                // 发送状态到多线程管理器，不弹窗
                if (_multiThreadManager != null)
                {
                    var status = new ThreadStatusInfo
                    {
                        Status = isManualMode ? ThreadStatus.WaitingForManualAction : ThreadStatus.Processing,
                        Message = message,
                        Title = title,
                        NeedsUserAction = isManualMode,
                        CurrentOperation = GetOperationFromDialogType(dialogType)
                    };

                    // 通过多线程管理器更新状态
                    _multiThreadManager.GetType()
                        .GetMethod("OnThreadStatusChanged", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?
                        .Invoke(_multiThreadManager, new object[] { threadId, status });

                    // 如果需要用户操作，触发手动操作事件
                    if (isManualMode)
                    {
                        _multiThreadManager.GetType()
                            .GetMethod("OnThreadManualActionRequired", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?
                            .Invoke(_multiThreadManager, new object[] { threadId, $"{title}: {message}" });
                    }
                }
                else
                {
                    _logService.LogWarning($"[线程{threadId}] 多线程管理器未设置，无法更新状态");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"[线程{threadId}] 多线程模式对话框处理异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据对话框类型获取操作描述
        /// </summary>
        private static string GetOperationFromDialogType(DialogType dialogType)
        {
            return dialogType switch
            {
                DialogType.Error => "错误处理",
                DialogType.Warning => "警告处理",
                DialogType.Success => "操作成功",
                DialogType.Question => "等待确认",
                DialogType.Information => "信息提示",
                _ => "状态更新"
            };
        }

        /// <summary>
        /// 检查是否为多线程模式
        /// </summary>
        public static bool IsMultiThreadMode(int threadId)
        {
            return threadId > 0;
        }

        /// <summary>
        /// 获取当前模式描述
        /// </summary>
        public static string GetModeDescription(int threadId)
        {
            return threadId == 0 ? "单线程模式" : $"多线程模式 (线程{threadId})";
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public static void Cleanup()
        {
            _multiThreadManager = null;
            _logService.LogInfo("DialogManager 已清理");
        }
    }

    /// <summary>
    /// 对话框类型枚举
    /// </summary>
    public enum DialogType
    {
        Information,    // 信息
        Warning,        // 警告
        Error,          // 错误
        Success,        // 成功
        Question        // 询问
    }

    /// <summary>
    /// 对话框扩展方法
    /// </summary>
    public static class DialogExtensions
    {
        /// <summary>
        /// 显示信息对话框的扩展方法
        /// </summary>
        public static void ShowInfo(this int threadId, string title, string message, bool isManualMode = false)
        {
            DialogManager.ShowDialog(threadId, title, message, isManualMode, DialogType.Information);
        }

        /// <summary>
        /// 显示错误对话框的扩展方法
        /// </summary>
        public static void ShowError(this int threadId, string title, string message)
        {
            DialogManager.ShowErrorDialog(threadId, title, message);
        }

        /// <summary>
        /// 显示警告对话框的扩展方法
        /// </summary>
        public static void ShowWarning(this int threadId, string title, string message)
        {
            DialogManager.ShowWarningDialog(threadId, title, message);
        }

        /// <summary>
        /// 显示成功对话框的扩展方法
        /// </summary>
        public static void ShowSuccess(this int threadId, string title, string message)
        {
            DialogManager.ShowSuccessDialog(threadId, title, message);
        }

        /// <summary>
        /// 显示确认对话框的扩展方法
        /// </summary>
        public static MessageBoxResult ShowConfirm(this int threadId, string title, string message, MessageBoxResult defaultResult = MessageBoxResult.No)
        {
            return DialogManager.ShowConfirmDialog(threadId, title, message, defaultResult);
        }
    }
}
